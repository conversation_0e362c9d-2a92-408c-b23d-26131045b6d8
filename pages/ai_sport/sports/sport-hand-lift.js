/**
 * 手平举运动分析器
 *
 * @alphaair
 * 20220303 created.
 **/

const utils = require("../utils/util");
const AiSport = requirePlugin("aiSport");
const SportBase = AiSport.sports.SportBase;
const Calculator = AiSport.calc.Calculator;

class SportHandLift extends SportBase {
    _calc = null
    _calculator = null

    stateTran = -1

    /**
     * 初始化分析器
     * 
     * @param {int?} pointThreshold 计算器取点评分阈值
     */
    constructor(pointThreshold) {
        super(pointThreshold);

        this.tickMode = true; //计次模式
        this._calc = new AiSport.calc.CalcBase();
        this._calculator = new Calculator();
        this._calculator.pointThreshold = this.pointThreshold;
        this.buildRules();
    }

    buildRules() {
        this.basePose = {
            name: '基本姿态',
            calc: '$and',
            rules: [{
                name: '全身需入镜',
                calc: 'whole'
            }, {
                name: '站立姿态',
                calc: 'stand'
            }, {
                name: '站立姿态',
                calc: 'stand'
            }, {
                name: '任一手平直',
                calc: '$or',
                rules: [{
                    name: '左平直状态',
                    calc: 'match-angle',
                    angleKey: 'left_shoulder',
                    secondKey: 'left_hip',
                    thirdKey: 'left_wrist',
                    angle: 90,
                    offset: 20
                }, {
                    name: '右平直状态',
                    calc: 'match-angle',
                    angleKey: 'right_shoulder',
                    secondKey: 'right_hip',
                    thirdKey: 'right_wrist',
                    angle: 90,
                    offset: 20
                }]
            }]
        };
    }

    pushing(body) {

        if (utils.isNone(body))
            return;

        //基本姿态不符合要求
        if (!this._calculator.calculating(body, {
                name: '站立姿态',
                calc: 'stand'
            })) {
            let f = this.invalid ? null : -2;
            this.invalid = true;
            return f;
        }

        if (!this._calculator.calculating(body, this.basePose))
            return;

        this.invalid = false;
        let nose = this._calc.findPoint(body.keypoints, 'nose');
        if (!nose)
            return;

        let wrist = null;
        let leftWrist = this._calc.findPoint(body.keypoints, 'left_wrist');
        let rightWrist = this._calc.findPoint(body.keypoints, 'right_wrist');
        if (!leftWrist || !rightWrist)
            return;

        if (leftWrist && rightWrist)
            wrist = leftWrist.score > rightWrist.score ? leftWrist : rightWrist;
        else
            wrist = leftWrist || rightWrist;

        //前伸算起始动作
        if (wrist.x > nose.x) {
            this.stateTran = 1;
            return;
        }

        //到达结束动作，计数1
        if (wrist.x >= nose.x || this.stateTran !== 1)
            return;

        this.stateTran = -1;
        this.countTimes();
        this.emitTick();
        console.log('on-tick');
    }
}

SportHandLift.KEY = "Hand-Lift";
SportHandLift.NAME = "自定义-手臂平伸运动";

module.exports = SportHandLift;