import xwyApi from '@/utils/api/xwy_api'

export default {
    spaceListNotLimit: null,
    currentManagedFriend: null, // 当前管理的亲友信息
    currentManagedFriendData: null, // 当前管理亲友的详细数据（包含分类和位置）
    
    async getSpaceListNotLimit(reload = false) {
        // 先获取基础的位置列表
        let baseList = []
        
        if (!this.currentManagedFriendData) {
            // 如果不是管理亲友，获取自己的位置列表
            if (this.spaceListNotLimit && !reload) return this.spaceListNotLimit
            
            const res = await xwyApi.request({
                url: 'front.user.category/category_list_not_limit',
                data: {
                    types: 33
                }
            })
            baseList = res?.data?.category_list || []
            this.spaceListNotLimit = baseList
        } else {
            // 如果正在管理亲友，合并系统位置和亲友位置
            // 获取系统位置
            const systemSpace = await this.getSystemSpace()
            // 亲友的位置列表
            const friendSpace = this.currentManagedFriendData.location_list || []
            // 合并两个列表
            baseList = [...systemSpace, ...friendSpace]
        }
        
        return baseList
    },
    
    
    async getSpacePath(id) {
        if (!id) return []
        const system_space = await this.getSystemSpace()
        const my_space = await this.getSpaceListNotLimit()
        const list = [...system_space, ...my_space]
        let space = list.find(v => v.id === id)
        if (!space) return []
        
        const path = []
        while (space) {
            path.unshift({id: space.id, pid: space.pid, name: space.name})
            space = list.find(v => v.id === space.pid)
        }
        return path
    },
    
    /**
     * @description 获取物品列表
     * @param {Object} data - 请求数据对象
     * @param {Number} [data.page] - 当前页码
     * @param {Number} [data.perpage] - 每页显示的物品数量
     * @param {String} [data.keyword] - 搜索关键词，可搜索物品名称，存放地址以及备注
     * @param {Number} [data.item_category_id] - 物品分类ID，筛选物品
     * @param {Number} [data.position_category_id] - 物品存放位置ID，筛选物品
     * @param {Number} [data.date_types] - 搜索的日期类型：
     *     1 - 购买时间搜索
     *     2 - 到期时间搜索
     *     3 - 生产日期
     *     4 - 开封日期
     * @param {String} [data.begin_time] - 开始时间，格式为 yyyy-mm-dd
     * @param {String} [data.end_time] - 结束时间，格式为 yyyy-mm-dd
     * @returns {Promise<Object>} - 返回物品列表数据的Promise对象
     */
    getItemList(data) {
        data.page ||= 1
        data.perpage ||= 20
        
        // 如果正在管理亲友的物品，添加管理标记并添加亲友userid
        const currentFriend = this.getCurrentManagedFriend()
        if (currentFriend && currentFriend.id) {
            data.is_helper = 1
            data.userid = currentFriend.id
        }
        
        return xwyApi.request({
            url: 'front.flat.sport_step.item_storage.manage/user_item_list',
            data
        })
    },
    
    initItemListData(list) {
        return list.map(item => {
            if (item.position_cate_details) delete item.position_cate_details
            if (item.item_cate_details) {
                item.category_name = item.item_cate_details.name
                delete item.item_cate_details
            }
            return item
        })
    },
    
    
    async getSystemSpace(reload = false, getAll = false) {
        const systemSet = await this.getSystemSet(reload)
        let system_space = systemSet.position_category || []
        
        system_space = system_space.map(item => {
            return {
                id: item.idx,
                name: item.name,
                logo: item.logo || '',
                level: 0,
                pid: 0
            }
        })
        
        if (!getAll) {
            const removed_id_list = uni.getStorageSync('item-storage-system-space-remove-id-list')
            if (removed_id_list?.length) {
                system_space = system_space.filter(item => !removed_id_list.includes(item.id))
            }
        }
        
        // 注意：不在这里合并亲友位置，避免重复
        // 合并逻辑统一在 getSpaceListNotLimit 中处理
        return system_space
    },
    
    async getSystemCategory() {
        // 获取系统分类
        const systemSet = await this.getSystemSet()
        const item_category = systemSet.item_category || []
        const systemCategories = item_category.map(item => {
            return {
                category_id: item.idx,
                name: item.name
            }
        })
        
        return systemCategories
    },
    
    systemSet: null,
    async getSystemSet(reload = false) {
        if (this.systemSet && !reload) return this.systemSet
        // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
        /** @namespace res.data.system_set */
        const res = await xwyApi.request({url: 'front.flat.sport_step.item_storage.manage/item_system_set'})
        const systemSet = res?.data?.system_set || {item_category: [], position_category: []}
        this.systemSet = systemSet
        return systemSet
    },
    
    myCategoryList: null,
    async getMyCategoryList(reload = false) {
        // 如果正在管理亲友的物品，返回亲友的自定义分类
        if (this.currentManagedFriendData && this.currentManagedFriendData.category_list) {
            return this.currentManagedFriendData.category_list.map(item => {
                return {
                    category_id: item.category_id,
                    name: item.name
                }
            })
        }
        
        if (!reload && this.myCategoryList) return this.myCategoryList
        
        const res = await xwyApi.getCategoryList({
            my_self: 1,
            types: 32,
            page: 1,
            perpage: 100
        })
        
        const list = res?.data?.category_list?.data || []
        this.myCategoryList = list
        return list
    },
    
    getItemDetailsKeys() {
        return ['name', 'logo', 'position_place', 'item_category_id', 'position_category_id', 'buy_date', 'product_date', 'expired_date', 'open_date', 'num', 'left_num', 'amount', 'brand_name', 'memo', 'sort_num']
    },
    
    // 获取当前管理的亲友信息
    getCurrentManagedFriend() {
        return this.currentManagedFriend
    },
    
    // 设置当前管理的亲友信息
    setCurrentManagedFriend(friendInfo) {
        this.currentManagedFriend = friendInfo
        this.currentManagedFriendData = null
        
        if (friendInfo && friendInfo.uuid) {
            // 只保存uuid
            uni.setStorageSync('item-storage-current-managed-friend-uuid', friendInfo.uuid)
        } else {
            uni.removeStorageSync('item-storage-current-managed-friend-uuid')
        }
        
        // 清空缓存的分类和空间数据
        this.spaceListNotLimit = null
        this.myCategoryList = null
    },
    
    // 初始化当前管理的亲友数据
    async initCurrentManagedFriendData() {
        const storedUuid = uni.getStorageSync('item-storage-current-managed-friend-uuid')
        if (!storedUuid) {
            this.currentManagedFriend = null
            this.currentManagedFriendData = null
            return
        }
        
        // 从服务器获取亲友详细数据
        const friendData = await this.getFriendDetails(storedUuid)
        if (friendData) {
            this.currentManagedFriend = friendData.share_friend_details
            this.currentManagedFriendData = friendData
        } else {
            // 如果获取失败，清除无效的uuid
            uni.removeStorageSync('item-storage-current-managed-friend-uuid')
            this.currentManagedFriend = null
            this.currentManagedFriendData = null
        }
    },
    
    // 获取亲友的详细信息和分类/位置数据
    async getFriendDetails(uuid) {
        const res = await xwyApi.request({
            url: 'front.user.account.shareManageRank/share_manage_user_details',
            data: {
                get_types: 1,
                uuid: uuid,
                page: 1,
                perpage: 1000
            }
        })
        
        if (res?.status === 1 && res?.data) {
            // 处理数据格式，统一分类和位置数据结构
            const data = res.data
            const details = data.share_friend_details || {}
            const share_friend_details = {
                id: details.id,
                uuid: details.uuid,
                nickname: details.nickname,
                headimg: details.headimg
                
            }
            return {
                share_friend_details,
                category_list: data.category_list?.goods?.data || [],
                location_list: data.category_list?.location || []
            }
        }
        return null
    }
}