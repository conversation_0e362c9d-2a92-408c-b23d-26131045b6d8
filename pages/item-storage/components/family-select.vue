<template>
    <view>
        <view class="container">
            <view @click="family_list_show = true">
                <image v-if="activeFamily.headimg" class="logo-image" :src="activeFamily.headimg"
                       mode="aspectFill"/>
                <view v-else class="no-logo text-center flex-all-center">
                    <uni-icons v-if="activeFamily.id === 0" type="person" size="24" color="#ffffff"/>
                    <text v-else>{{ activeFamily.nickname[0] }}</text>
                </view>
            </view>
            
            <view v-show="family_list_show">
                <view class="mask" @click="family_list_show = false"></view>

                <view class="triangular-arrow"></view>

                <view class="family-list bg-white">
                    <view class="family-item flex-row" v-for="item in family_list" :key="item.id"
                          :class="{'active-family': item.uuid === uuid}"
                          hover-class="navigator-hover" @click="changeFamily(item.uuid)">
                        <view class="pr10">
                            <image v-if="item.headimg" class="logo-image" :src="item.headimg"
                                   mode="aspectFill"/>
                            <view v-else class="no-logo flex-all-center">
                                <uni-icons v-if="item.id === 0" type="person" size="24" color="#ffffff"/>
                                <text v-else>{{ item.nickname[0] }}</text>
                            </view>
                        </view>
                        <view class="nickname ellipsis">{{ item.nickname }}</view>
                    </view>

                    
                    <view v-if="!isLastPage" class="family-item flex-row" hover-class="navigator-hover"
                          @click="toFamilyList">
                        <view class="pr10">
                            <view class="no-logo flex-all-center">
                                <text class="iconfont icon-team font24 color-white"></text>
                            </view>
                        </view>
                        <view class="nickname">查看更多</view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import _API_ from '../api'

export default {
    name: "family-select",
    emits: ['familyChange', 'toFamily'],
    data() {
        return {
            family_list_show: false,
            uuid: '',
            family_list: [],
            isLastPage: false,
            myself: {} // 添加 myself 数据
        }
    },

    computed: {
        activeFamily() {
            return this.family_list.find(item => item.uuid === this.uuid) || {}
        }
    },

    mounted() {
        this.init()
    },

    methods: {
        async init() {
            this.getMyself()
            await this.getFamilyList()
            this.getCurrentManagedFriend()
        },

        getMyself() {
            const {id, userid: uuid, headimg} = app.globalData.userinfo || {}
            this.myself = { id, uuid, nickname: '我自己', headimg}
        },

        getCurrentManagedFriend() {
            const currentFriend = _API_.getCurrentManagedFriend()
            this.uuid = currentFriend?.uuid || this.myself.uuid
        },

        async getFamilyList() {
            const res = await this.xwy_api.request({
                url: 'front.user.account.shareManageRank/share_manage_user_list',
                data: {
                    page: 1,
                    perpage: 5,
                    is_helper: 1
                }
            })

            let list = [], isLastPage = true

            const result = res?.data?.list
            if (result) {
                list = this.initListData(result.data || [])
                isLastPage = result.is_lastpage
            }

            this.family_list = [this.myself, ...list]
            this.isLastPage = isLastPage
        },

        initListData(list) {
            return list.map(item => {
                const details = item.belong_user_details || {}

                return {
                    id: details.id || 0,
                    uuid: details.uuid || '',
                    nickname: details.nickname || '',
                    headimg: details.headimg || '',
                }
            })
        },

        toFamilyList() {
            // 跳转到亲友列表页面
            this.family_list_show = false
            this.$uni.navigateTo('../family/list?type=2', {
                events: {
                    familyChange: () => this.$emit('familyChange')
                }
            })
        },

        async changeFamily(uuid) {
            // 如果选择的是当前已选中的亲友，不做处理
            if (uuid === this.uuid) {
                this.family_list_show = false
                return
            }
            
            // 查找选中的亲友信息
            const friend = this.family_list.find(item => item.uuid === uuid)
            if (!friend) return
            
            // 处理切换逻辑
            if (uuid === this.myself.uuid) {
                // 切换回自己
                _API_.setCurrentManagedFriend(null)
                this.$uni.showToast('已切换至管理自己的物品')
                this.uuid = uuid
            } else {
                // 切换到亲友
                this.$uni.showLoading('切换中...')
                
                // 获取亲友的详细信息
                const friendData = await _API_.getFriendDetails(friend.uuid)
                uni.hideLoading()
                
                if (!friendData) {
                    this.$uni.showToast('获取亲友信息失败')
                    return
                }
                
                // 设置当前管理的亲友（只保存uuid）
                _API_.setCurrentManagedFriend(friend)
                
                // 设置详细数据到内存
                _API_.currentManagedFriend = friendData.share_friend_details
                _API_.currentManagedFriendData = friendData
                
                this.$uni.showToast(`已切换至管理${friend.nickname}的物品`)
                this.uuid = uuid
            }
            
            // 关闭下拉菜单
            this.family_list_show = false
            
            // 触发父组件的familyChange事件，让父组件重新加载数据
            this.$emit('familyChange')
        },
    }
}
</script>

<style lang="scss" scoped>
.container {
    position: relative;

    $size: 40px;

    .logo-image, .no-logo {
        width: $size;
        height: $size;
        border-radius: 50%;
        background-color: #5cadff;
        color: #fff;
    }

    .logo-image {
        display: block;
    }

    .mask {
        position: fixed;
        z-index: 9999;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background-color: rgba(0, 0, 0, 0.7);
    }

    .triangular-arrow {
        position: absolute;
        z-index: 10000;
        top: 50px;
        left: 10px;
        width: 20px;
        height: 20px;
        background-color: #fff;
        transform: rotate(45deg);
    }

    .family-list {
        position: absolute;
        z-index: 10000;
        top: 60px;
        left: -10px;
        border-radius: 10px;
        padding: 5px;

        .family-item {
            border-radius: 5px;
            padding: 5px;

            .nickname {
                width: 100px;
                line-height: $size;
                color: #5cadff;
                font-size: 14px;
            }
        }

        .active-family {
            background-color: #d4e6f8;
        }
    }
}
</style>