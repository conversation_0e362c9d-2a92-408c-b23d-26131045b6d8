<template>
    <view>
        <uni-popup ref="popup" type="bottom" :safe-area="false" @touchmove.stop.prevent="">
            <view class="popup radius10 bg-white">
                
                <view v-if="p_id && p_id !== 'null'" class="font14 color-sub flex-row">
                    <view class="pr5" style="white-space: nowrap;">当前位置:</view>
                    <space-path :space-id="p_id || 0"/>
                </view>


                <scroll-view class="space-scroll-view" :scroll-y="true" :scroll-into-view="spaceScrollViewId">
                    <view class="space-level" v-for="item in space_list" :key="item.pid">
                        <view class="font12 color-sub" v-if="item.parent">{{ item.parent.name }}下的位置</view>
                        <view class="space-list flex-row flex-wrap">
                            <template v-for="space in item.space_list">
                                <view class="space-item" :class="{'space-item-active': space.active}"
                                      v-if="!space.hidden" :key="space.id" @click="chooseSpace(space)">
                                    {{ space.name }}
                                </view>
                            </template>
                        </view>
                    </view>
                    
                    <view :id="spaceScrollViewId"></view>
                </scroll-view>
                
                
                <view class="confirm-button color-white bg-primary text-center" @click="confirm">确定</view>
                
                <view class="flex-all-center">
                    <view class="p5 color-sub font12" @click="$refs.popup.close()">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import _API_ from '../api'
import spacePath from "./space-path.vue"

export default {
    name: "space-choose",
    components: {spacePath},
    emits: ['confirm'],
    props: {
        pid: {
            type: Number,
            default: null
        },

        // 是否是修改物品位置存放位置(父位置)的，是的话，不能选自己，需要过滤掉自己
        editSpace: {
            type: Boolean,
            default: false
        },
        
        // 修改物品位置存放位置(父位置)的，除了传父id，还要传自己的id
        myId: {
            type: Number,
            default: null
        }
    },
    data() {
        return {
            all_space_list: [],
            p_id: 'null'
        }
    },
    
    watch: {
        pid() {
            this.p_id = this.pid || 'null'
        }  
    },
    
    computed: {
        space_list() {
            if (this.p_id && this.p_id !== 'null') return this.getSpaceList()
            
            const space_list = this.getAllSpaceList().filter(item => item.level === 0)
            space_list.forEach(item => {
                if (item.id === 'null') item.active = true
            })

            return [{
                pid: 0,
                space_list
            }]
        },

        spaceScrollViewId() {
            const space_list = this.space_list
            if (!space_list?.length) return ''
            const last_level = space_list[space_list.length - 1]
            if (!last_level) return ''
            const last_space_list = last_level.space_list
            if (!last_space_list?.length) return ''
            const last_space = last_space_list[last_space_list.length - 1]
            if (last_space?.id) return `space-scroll-view-${last_space.id || ''}`
            return ''
        }
    },

    methods: {
        async open() {
            // 直接使用 getSpaceListNotLimit，它已经包含了系统位置和用户/亲友位置
            let list = await _API_.getSpaceListNotLimit()
            list = list.map(item => {
                return {
                    id: item.id,
                    pid: item.pid,
                    name: item.name,
                    level: item.level
                }
            })
            
            if (this.editSpace && this.myId) {
                // 修改物品位置存放位置(父位置)的，不能选自己，需要隐藏自己，不能直接过滤，直接过滤会导致后面找自己的时候找不到，代码报错
                list.forEach(item => {
                    if (item.id === this.myId) item.hidden = true
                })
            }

            list.unshift({
                id: 'null',
                pid: 0,
                name: '不指定',
                level: 0
            })
            
            this.all_space_list = list
            
            this.$refs.popup.open()
        },

        getAllSpaceList() {
            return JSON.parse(JSON.stringify(this.all_space_list))
        },
        
        
        setActiveSpace(list, id) {
            return list.map(item => {
                item.active = item.id === id
                return item
            })
        },
        
        
        // 从下往上递归，找到每一级的位置
        getSpaceList() {
            const all_space_list = this.getAllSpaceList()
            let pid = this.p_id
            const list = []
            let space = all_space_list.find(item => item.id === pid)
            const next_level = this.getNextLevelSpace(space)
            if (next_level?.length) list.unshift({
                pid: next_level[0].pid,
                parent: space,
                space_list: next_level
            })
            const same_level = this.getSameLevelSpace(space)

            pid = space.pid
            
            list.unshift({
                pid,
                parent: all_space_list.find(item => item.id === pid),
                space_list: this.setActiveSpace(same_level, space.id)
            })

            

            while (pid) {
                const space = all_space_list.find(item => item.id === pid)
                const same_level = this.getSameLevelSpace(space)
                pid = space.pid
                list.unshift({
                    pid,
                    parent: all_space_list.find(item => item.id === pid) || null,
                    space_list: this.setActiveSpace(same_level, space.id)
                })

                
            }

            return list
        },
        
        // 找到同级并且pid一样的
        getSameLevelSpace(space) {
            return this.getAllSpaceList().filter(item => item.pid === space.pid && item.level === space.level)
        },
        
        // 找下一级的
        getNextLevelSpace(space) {
            return this.getAllSpaceList().filter(item => item.pid === space.id)
        },

        chooseSpace(space) {
            this.p_id = space.id
        },

        confirm() {
            this.$refs.popup.close()
            const id = this.p_id === 'null' ? null : (this.p_id || null)
            this.$emit('confirm', id)
        }
    }
}
</script>

<style lang="scss">
.popup {
    padding: 10px;
    
    .space-scroll-view {
        height: 50vh;
        
        .space-level {
            padding: 10px 0;

            .space-item {
                background-color: #f8f8f8;
                color: #333;
                font-size: 14px;
                padding: 10px;
                margin: 5px;
                border-radius: 2px;
            }
            
            .space-item-active {
                background-color: #5cadff;
                color: #fff;
            }
        }
    }
    
    .confirm-button {
        margin: 0 auto;
        width: 150px;
        line-height: 40px;
        border-radius: 20px;
    }
}
</style>