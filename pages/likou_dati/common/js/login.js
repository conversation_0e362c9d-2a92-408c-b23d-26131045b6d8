import xwyLib from "./public.js";

export default {
    init(cb) {
        let app = getApp();
        if (!uni.getStorageSync(`${xwyLib.ext_conf.who}access_token-${xwyLib.cur_time}`)) {
            xwyLib.clear_storage(() => {}, 1);
            xwyLib.clear_storage(() => {}, 2);
            // #ifdef MP-WEIXIN
            console.log('微信登录，，，');
            this.wechatLogin(cb);
            return
            // #endif

            // #ifndef MP-WEIXIN
            this.h5Login();
            // #endif
            return
        } else {
            app.globalData.access_token = uni.getStorageSync(`${xwyLib.ext_conf.who}access_token-${xwyLib.cur_time}`);
        }

        if (!app.globalData.userInfo || !app.globalData.shopconfset) {
            this.get_userInfo(cb)
            return
        }

        cb && cb();
    },

    h5Login() {
        let pageUrl = xwyLib.get_cur_page_and_params();
        uni.setStorageSync('pageUrl', pageUrl);
        let url = '/pages/login/password-login/password-login'
        xwyLib.routeJump(url, 'reLaunch');
    },

    // 登录 微信登录
    wechatLogin(cb) {
        xwyLib.showLoading();
        uni.login({
            "provider": 'weixin',
            success: e => {
                console.log('微信登录获取code', e);
                this.get_openid(e.code, cb);
            },
            fail: (err) => {
                uni.hideLoading()
                console.log('获取code失败', err);
            }
        })
    },

    // 获取openid
    async get_openid(code, cb) {
        let app = getApp();
        let data = {
            js_code: code,
            shopid: parseInt(xwyLib.ext_conf.who),
        };
        let obj = {
            data,
            url: 'front.user.wechat.login/code2session',
            success: res => {
                console.log('获取openid请求的参数' + JSON.stringify(data));
                console.log(`获取openid---${JSON.stringify(res)}`);
                uni.hideLoading();
                if (res.status != 1) {
                    xwyLib.alert(`用户登录---${JSON.stringify(res)}`)
                    uni.hideLoading();
                    return
                }

                if (!res.data.access_token || !res.data.wechat_res || !res.data.wechat_res.openid) {
                    xwyLib.alert('登录失败！');
                    uni.hideLoading();
                    return
                }

                app.globalData.access_token = res.data.access_token;
                app.globalData.wechat_res = res.data.wechat_res;
                uni.setStorageSync(`${xwyLib.ext_conf.who}access_token-${xwyLib.cur_time}`, res
                    .data
                    .access_token);
                uni.setStorageSync(`${xwyLib.ext_conf.who}openid-${xwyLib.cur_time}`, res.data
                    .wechat_res
                    .openid);

                this.get_userInfo(cb);
            },
            fail: res => {
                console.log('获取openid---fail', res);
            },
            compelet: res => {
                console.log('获取openid---compelet', res);
            }
        };
        xwyLib.ajax(obj);
    },


    // 获取用户信息
    async get_userInfo(cb, newUserinfo = '') {
        xwyLib.showLoading();
        let app = getApp();
        if (uni.getStorageSync(`${xwyLib.ext_conf.who}userInfo${xwyLib.cur_time}`) && !newUserinfo) {
            let userInfo = JSON.parse(uni.getStorageSync(`${xwyLib.ext_conf.who}userInfo${xwyLib.cur_time}`))
            app.globalData.userInfo = userInfo;
            this.get_shopconfset(cb)
            return
        }

        let data = {
            access_token: app.globalData.access_token,
        };
        let obj = {
            data,
            url: `front.user.user/get_user_details`,
            success: res => {
                console.log('获取用户信息', res);
                if (res.status != 1 || !res.data || !res.data.user_details) {
                    xwyLib.alert(res.info);
                    return
                }

                if (!newUserinfo) {
                    xwyLib.clear_storage(() => {}, 1);
                }

                // #ifdef MP-WEIXIN
                if (!res.data.user_details.nickname) {
                    let user_obj = {
                        nickname: '微信用户',
                        headimg: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/shop/9/90/2022/04/14/6257e06f3581f_5652.jpg',
                    }
                    login.upload_userInfo(user_obj)
                    res.data.user_details.nickname = user_obj.nickname;
                    res.data.user_details.headimg = user_obj.headimg;
                }
                // #endif
                app.globalData.userInfo = res.data.user_details;
                uni.setStorageSync(`${xwyLib.ext_conf.who}userInfo${xwyLib.cur_time}`, JSON.stringify(res
                    .data
                    .user_details));
                if (!newUserinfo) {
                    this.get_shopconfset(cb)
                    return
                }
                uni.hideLoading();
                cb && cb(res)
            }
        };
        xwyLib.ajax(obj);
    },

    // 获取商户个性化设置
    async get_shopconfset(cb) {
        let app = getApp();
        if (uni.getStorageSync(`${xwyLib.ext_conf.who}shopconfset${xwyLib.cur_time}`)) {
            let shopconfset = JSON.parse(uni.getStorageSync(`${xwyLib.ext_conf.who}shopconfset${xwyLib.cur_time}`))
            app.globalData.shopconfset = shopconfset;
            let page_diy_set = app.globalData.shopconfset?.shop_set?.page_diy_set;
            // 获取导航栏的颜色 如果存在导航栏颜色则赋值
            if (page_diy_set?.navigation_bar_color_set) {
                let fzColor = page_diy_set?.navigation_bar_color_set?.color || '#000000';
                let bgColor = page_diy_set?.navigation_bar_color_set?.background_color || '#fff';
                if (fzColor == 'black') fzColor = '#000000';
                if (fzColor == 'white') fzColor = '#ffffff';
                xwyLib.setNavigationBarColor({
                    fzColor,
                    bgColor,
                });
            }

            uni.hideLoading();
            cb && cb();
            return
        }
        let obj = {
            data: {
                access_token: app.globalData.access_token,
                types: 1,
            },
            url: `front.user.shop/shopconf`,
            success: res => {
                console.log('获取shopconf', res);
                uni.hideLoading();
                if (res.status != 1 || !res.data) {
                    xwyLib.alert(res.info)
                    return
                }
                let shopconfset = res.data;
                app.globalData.shopconfset = shopconfset;
                uni.setStorageSync(`${xwyLib.ext_conf.who}shopconfset${xwyLib.cur_time}`, JSON.stringify(res
                    .data))
                cb && cb();
            }
        };
        xwyLib.ajax(obj);
    },

    // 更新用户信息
    async upload_userInfo(opt = {}) {
        if (!opt.nickname) {
            xwyLib.alert('请输入用户昵称！')
            return
        }

        if (opt.mobile && opt.mobile.length < 11) {
            xwyLib.alert('请正确输入手机号码！')
            return
        }

        let app = getApp();
        let params = {
            access_token: app.globalData.access_token,
            shopid: parseInt(xwyLib.ext_conf.who),
        };

        // 昵称
        if (opt.nickname) params.nickname = opt.nickname;

        // 头像
        if (opt.headimg) params.headimg = opt.headimg;

        // 手机号码
        if (opt.mobile) params.mobile = opt.mobile;

        // 性别
        if (opt.gender) params.gender = opt.gender;

        // 邮箱
        if (opt.email) params.email = opt.email;

        let obj = {
            data: params,
            url: `front.user.user/update_user_details`,
            success: res => {
                console.log('更新用户信息', res);
                if (res.status != 1) {
                    xwyLib.alert(res.info);
                    return
                }

                this.get_userInfo(() => {
                    opt.success && opt.success(res);
                }, 1)
            }
        };
        xwyLib.ajax(obj);
    },

}
