let defaultConfig = {
	// 要显示的key
	key: 'name',
	// 字体大小 rpx
	fontSize: 32,
	// 字体颜色
	color: '#313131',
	// 激活字体颜色
	activeColor: '#007AFF',
	// item宽度 0为自动
	itemWidth: 0,
	// 下划线左右边距，文字宽度加边距，如果设置了itemWidth则为itemWidth加边距 rpx
	underLinePadding: 10,
	// 下划线宽度 rpx  注意：设置了此值 underLinePadding 失效
	underLineWidth: 0,
	// 下划线高度 rpx
	underLineHeight: 4,
	// 下划线颜色
	underLineColor: '#007AFF',
};

export default defaultConfig
