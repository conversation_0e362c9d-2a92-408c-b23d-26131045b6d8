<template>
	<view class='empty-box f-d-c-j-a-c'>
		<image :src="imgurl" mode="widthFix"/>
		<view class="text">
			<text class="gray_color">{{text}}</text>
		</view>
	</view>
</template>

<script>
	let app = getApp();
	export default {
		data() {
			return {
				imgurl: '',
			}
		},
		props: {
			text: {
				type: String,
				default: '暂无数据',
			},
		},
		mounted() {
                this.imgurl = app.globalData.shopconfset.have_no_data || this.imgImport.not_dataimg;
		},
	}
</script>
<style scoped lang='scss'>
	@import url('@/pages/likou_dati/common/css/common.scss');
	@import url('@/pages/likou_dati/common/css/iconfont.css');
	@import url('@/pages/likou_dati/common/css/public.css');

	.empty-box {
		width: 100%;
		padding: 5rem 0;

		>image {
			width: 50%;
		}

		>.text {
			padding-top: .5rem;
		}
	}
</style>
