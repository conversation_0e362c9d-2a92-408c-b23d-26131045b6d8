<template>
    <view class="fixed_modal">
        <view class="fixed_modal_content p0">
            <view class="top p1">
                <view class="input-title pb1">
                    请填写以下信息：
                </view>
                <!-- 考题密码 -->
                <view v-if="exam_password" class="input-box pb1 f">
                    <view class="left f-a-c memo_text">
                        考卷密码
                        <text class="red_color">*</text>
                    </view>
                    <uni-easyinput class="right" type="password" v-model="password" :placeholder="`请输入考试需要的密码`" />
                </view>
                <view v-if="must_submit && must_submit.length" class="input-box f pb1"
                    v-for="(item,index) in must_submit" :key="index">
                    <view class="left f-a-c memo_text">
                        {{item.title}}
                        <text v-if="item.rules == 1" class="red_color">*</text>
                    </view>
                    <uni-easyinput v-if="item.types == 1" class="right" v-model="item.value"
                        :placeholder="`请输入${item.title}`" />
                    <view v-if="item.types == 2" class="selector">
                        <!-- 文本输入还是下拉选择 -->
                        <picker mode="selector" :value="item.option_idx" :range="item.option" :data-index="index"
                            @change="change_option" range-key="text">
                            <view class="selector-box">
                                <view class="selector-item f-j-sb-a-c">
                                    <view>{{item.option[item.option_idx].text}}</view>
                                    <i class="iconfont">&#xe6a6;</i>
                                </view>
                            </view>
                        </picker>
                    </view>
                </view>
            </view>

            <view class="btn-box f-j-sb-a-c gray_bdrtsolid">
                <view class="btn f-j-a-c gray_color" @click.stop="close_btn">
                    取消
                </view>
                <view class="btn f-j-a-c blue_color" @click.stop="save_must_submit">
                    保存
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    let app = getApp();
    export default {
        data() {
            return {
                password: '',
                must_submit: '',
            }
        },
        props: {
            all_must_submit: {
                default: '',
            },
            activityID: {
                default: '',
            },
            exam_id: {
                default: '',
            },
            name: {
                type: String,
                default: '',
            },
            exam_password: {
                type: String,
                default: '',
            },
            types: {
                default: -1,
            }
        },
        mounted() {
            this.$nextTick(function() {
                if (!this.all_must_submit || !Array.isArray(this.all_must_submit)) {
                    console.log('this.all_must_submit', this.all_must_submit);
                    this.xwyLib.alert('实名信息加载失败，请刷新页面重新获取！');
                    this.close_btn();
                    return
                }
                let must_submit = this.all_must_submit;
                must_submit.forEach((val, idx) => {
                    if (val.rules == 1 && val.types == 2) val.value = val.option[0].text
                })
                this.must_submit = must_submit;
            })
        },
        methods: {
            close_btn() {
                this.$emit('close_btn');
            },
            // 判断信息验证参数
            judge_must_submit_params() {
                let must_submit = this.must_submit;
                for (let i = 0; i < must_submit.length; i++) {
                    if (must_submit[i].rules == 1 && !must_submit[i].value && must_submit[i].value != 0) {
                        this.xwyLib.alert(`${must_submit[i].title}为必填项，请填写${must_submit[i].title}!`)
                        return false;
                    }
                }
                return true
            },

            save_must_submit() {
                this.must_submit_post();
            },

            // 实名信息验证
            async must_submit_post() {
                if (!this.judge_must_submit_params()) return
                this.xwyLib.showLoading();
                if (this.activityID) uni.setStorageSync(`must_submit_active${this.activityID}`, this.xwyLib
                    .jsonEncode(this.must_submit))
                if (this.exam_id && !this.activityID) uni.setStorageSync(`must_submit_exam${this.exam_id}`, this
                    .xwyLib.jsonEncode(this
                        .must_submit))


                if (this.exam_id && this.activityID) {
                    this.attend_post();
                    return
                }
                this.close_btn();
                this.skip_details();
            },

            // 跳转到活动详情
            skip_details() {
                let url = '';
                // 活动
                if (this.activityID) {
                    if (this.types == 1) url =
                        `/pages/tree_activity/tree_details/tree_details?activityID=${this.activityID}`;
                    if (this.types == 3) url =
                        `/pages/sign_activity/sign_details/sign_details?activityID=${this.activityID}`;
                }
                // 只有考试本身
                if (this.exam_id) url = `/pages/likou_dati/pages/answer/answer_question/answer_question?exam_id=${this.exam_id}`;
                // 在活动中的考试
                if (this.activityID && this.exam_id) url =
                    `/pages/likou_dati/pages/answer/answer_question/answer_question?exam_id=${this.exam_id}&activityID=${this.activityID}`;

                this.xwyLib.routeJump(url);
            },


            change_option(e) {
                let index = e.currentTarget.dataset.index,
                    value = e.detail.value;

                this.$nextTick(function() {
                    this.$set(this.must_submit[index], 'option_idx', value);
                    this.$set(this.must_submit[index], 'value', this.must_submit[index].option[value].text);
                })
            },


            // 报名
            attend_post() {
                this.xwyLib.showLoading();
                let must_submit = '';
                if (uni.getStorageSync(`must_submit_active${this.activityID}`)) must_submit = uni.getStorageSync(
                    `must_submit_active${this.activityID}`);
                let data = {
                    active_id: this.activityID,
                    must_submit,
                };
                
                let obj = {
                    data,
                    url: `front.flat.sport_step.user/submit_attend_active`,
                    success: res => {
                        console.log('报名', res);
                        uni.hideLoading();
                        if (res.status != 1) {
                            this.xwyLib.alert(res.info);
                            return
                        }
                        this.close_btn();
                        this.skip_details();
                    }
                };
                this.xwyLib.ajax(obj);
            },


        }
    }
</script>

<style scoped lang='scss'>
	@import url('@/pages/likou_dati/common/css/common.scss');
	@import url('@/pages/likou_dati/common/css/iconfont.css');
	@import url('@/pages/likou_dati/common/css/public.css');
	
    .fixed_modal_content {
        position: relative;

        .top {
            padding-bottom: 60px !important;
        }

        .btn-box {
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            background-color: #fff;

            .btn {
                width: 50%;
                text-align: center;
                padding: .7rem 0;

                &:first-child {
                    border-right: 1px solid #eee;
                }
            }
        }
    }

    .input-title {
        font-size: 1.1rem;
        font-weight: 700;
    }

    .input-box {
        width: 100%;

        >.left {
            width: 5rem;
        }

        >.right {
            width: calc(100% - 5rem);
        }

        .selector {
            width: calc(100% - 5rem);

            .selector-box {
                /* width: calc(100% - 5rem); */
                width: 100%;

                .selector-item {
                    padding: .45rem .5rem;
                    border-radius: .2rem;
                    border: 1px solid #ddd;
                    font-size: .9rem;
                }
            }
        }
    }
</style>
