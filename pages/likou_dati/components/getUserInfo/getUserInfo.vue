<template>
	<view class="popup-box f-j-a-c">
		<view class="popup-content">
			<view class="logo f-j-a-c">
				<image class="img" :src="logo" />
			</view>
			<view class="shop_name" style="text-align: center;">{{shop_name}}</view>
			<view class="text gray_color">
				此小程序由{{shop_name}}定制，即将会使用您的公开信息(名称、头像等)进入小程序，请您确认！
			</view>
			<view class="btn-box f-j-sb-a-c">
				<view class="btn red_bgcolor f-j-a-c" @click.stop="skipPage">
					取消
				</view>
				<view class="btn blue_bgcolor f-j-a-c" @click.stop="getUserProfile">
					授权
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	let app = getApp();
    
	export default {
		data() {
			return {
				logo: '',
				shop_name:'',
			}
		},
		mounted() {
            let logo = this.imgImport.not_headimg,
                shop_name = 'xx';
            
            if (app.globalData?.shopconfset?.shop_set?.shop_details?.logo) logo = app.globalData.shopconfset.shop_set
                .shop_details.logo;
            if (app.globalData?.shopconfset?.shop_set?.shop_details?.shop_name) shop_name = app.globalData.shopconfset
                .shop_set.shop_details.shop_name;
            
            this.logo = logo;
            this.shop_name = shop_name;
		},
		props: {
			currUrl: {
				type: String,
			},
		},
		methods: {
			getUserProfile(e) {
				uni.getUserProfile({
					desc: `获取你的昵称、头像！`,
					success: res => {
						// console.log(`获取授权`, res);
						if (res.errMsg != "getUserProfile:ok") {
							this.xwyLib.alert(`获取授权失败！`)
							return
						}
						let opt = {
							nickname: res.userInfo.nickName,
							headimg: res.userInfo.avatarUrl,
							success: response => {
								this.skipPage();
							}
						};
						this.xwyLib.upload_userInfo(opt)
					}
				})
			},

			skipPage() {
				this.xwyLib.routeJump(this.currUrl, 'reLaunch');
			},
		}
	}
</script>
<style scoped lang='scss'>
	.popup-box {
		position: fixed;
		left: 0;
		top: 0;
		z-index: 999999;
		width: 100vw;
		height: 100vh;
		background-color: #f8f8f8;

		.popup-content {
			width: 90vw;
			max-width: 330px;
			box-sizing: border-box;
			box-shadow: 0 0 1rem #bcbcbc;
			border-radius: .5rem;
			padding: 1rem;
			margin: 0 auto;
			background-color: #fff;
			
			.logo{
				width: 100%;
				
				image{
					width: 5rem;
					height: 5rem;
					border-radius: 50%;
				}
			}
			
			.shop_name{
				font-size: 1.2rem;
				padding-bottom: .5rem;
			}

			.text {
				font-size: 1rem;
				padding-bottom: 1rem;
			}

			.btn-box {
				width: 100%;

				.btn {
					width: 40%;
					border-radius: 10rem;
					padding: .5rem 0;
				}
			}
		}
	}
</style>
