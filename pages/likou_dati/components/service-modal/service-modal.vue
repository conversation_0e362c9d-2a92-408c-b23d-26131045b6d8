<template>
	<view class='empty-box f-d-c-j-a-c'>
		<view class="empty-item">
			<view class="text-box">
				<text class="gray_color f-j-a-c">{{text}}</text>
			</view>
			<view class="empty-btn-box f gray_color">
				<view class="btn f-j-a-c" style="border-right: 1px solid #eee" @click.stop="close_modal">
					取消
				</view>
				<button class="btn f-j-a-c blue_color" open-type="contact">
					联系客服
				</button>
			</view>

		</view>
	</view>
</template>

<script>
	let app = getApp();
	export default {
		data() {
			return {}
		},
		props: {
			text: {
				type: String,
				default: '暂无数据，请联系客服!',
			},
		},
		methods:{
			close_modal(){
				this.$emit('closeServiceModal', false)
			},
		},
	}
</script>
<style scoped lang='scss'>
	@import url('@/pages/likou_dati/common/css/common.scss');
	@import url('@/pages/likou_dati/common/css/iconfont.css');
	@import url('@/pages/likou_dati/common/css/public.css');
	
	.empty-box {
		position: fixed;
		left: 50%;
		transform: translateX(-50%);
		top: 0;
		width: 100%;
		max-width: 640px;
		height: 100%;
		background-color: rgba($color: #000, $alpha: .5);
		z-index: 29;
		
		.empty-item{
			width: 70%;
			max-width: 640px;
			background-color: #fff;
			border-radius: .2rem;
			
			.text-box {
				padding: 2rem 1rem;
			}
			
			.empty-btn-box{
				width: 100%;
				border-top: 1px solid #eee;
				
				.btn{
					width: 50%;
					padding: .7rem 0;
					line-height: 1.6;
					font-size: 1rem;
				}
				
				button{
					background-color: transparent;
					
					&:after{
						border: none;
					}
				}
				
			}
		}
		
	}
</style>
