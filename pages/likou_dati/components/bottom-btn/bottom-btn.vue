<template>
    <view v-if="length>0" class="tabbar-box">
        <view class="btn-box" :class="length > 1?'f-j-sb-a-c':'f-j-a-c'">
            <view class="btn f-j-a-c" :class="item.cancel ? 'gray_bdrsolid gray_color': 'blue_bgcolor blk_shadow_02'"
                :style="{'width': length > 1? `calc((100% - ${length - 1}rem) / ${length})`:'70%'}"
                v-for="(item,index) in btn_list" :key="index" @click.stop="btn_click(item)">
                {{item.text}}
            </view>
        </view>
    </view>
</template>

<script>
    let app = getApp();
    export default {
        data() {
            return {
                btn_list: [{
                    text: '完成',
                    clickFun: 'oneClick',
                }],
                length: 0,
            }
        },
        props: {
            btnList: {
                type: Array,
            }
        },
        mounted() {
            if (this.btnList) this.btn_list = this.btnList;
            this.length = this.btn_list.length;
        },
        methods: {
            btn_click(item) {
                if(item.clickFun) this.$emit(item.clickFun);
            },
        }
    }
</script>
<style scoped lang='scss'>
	@import url('@/pages/likou_dati/common/css/common.scss');
	@import url('@/pages/likou_dati/common/css/iconfont.css');
	@import url('@/pages/likou_dati/common/css/public.css');
</style>
