<template>
    <view class='content'>
        <view class="container">
            <!-- 选择从我的题库中出题还是从公共题库出题 -->
            <view class="question-item f-a-c">
                <view class="left">
                    题目来源：
                </view>
                <view class="right">
                    <select-lay :value="question_rules_conf.self_or_public" name="name" :options="self_or_public"
                        :zindex="11" @selectitem="select_self_or_public" placeholder=""></select-lay>
                </view>
            </view>

            <!-- 从题库中出题 -->
            <view v-if="question_rules_conf.self_or_public != 3" class="question-bank-box">
                <!-- 选择从全部题库还是单一题库出题 -->
                <view class="question-item f-a-c">
                    <view class="left">
                        选择题库：
                    </view>
                    <view class="right">
                        <select-lay
                            :value="question_rules_conf.rand_category_id ? category_list.findIndex(v => v.category_id === question_rules_conf.rand_category_id) : 0"
                            name="name"
                            :options="category_list"
                            :zindex="9"
                            @selectitem="select_category_list"
                            placeholder=""
                        ></select-lay>
                    </view>
                </view>
                <!-- 选择出题规则 -->
                <view class="question-item f-a-c">
                    <view class="left">
                        出题规则：
                    </view>
                    <view class="right">
                        <select-lay :value="question_rules_conf.rand_question_types" name="name"
                            :options="rand_question_types" :zindex="8" @selectitem="select_rand_question_types"
                            placeholder=""></select-lay>
                    </view>
                </view>

                <uni-table v-if="question_rules_conf.rand_question_types == 1" border stripe emptyText="暂无更多数据"
                    class="question-types-box">
                    <!-- 表头行 -->
                    <uni-tr>
                        <uni-th align="center" width="90">题目类型</uni-th>
                        <uni-th align="center">数量</uni-th>
                    </uni-tr>
                    <!-- 表格数据行 -->
                    <uni-tr v-for="(item,index) in question_types_num" :key="index">
                        <uni-td align="center">{{item.title}}</uni-td>
                        <uni-td align="center" class="f">
                            <view class="inp f-a-c">
                                <view class="left">
                                    <uni-easyinput type="number" :clearable="false"
                                        style="text-align: center;width: 100%;" v-model="item.num"
                                        :placeholder="`输入${item.title}题的数量`" @input="change_question" />
                                </view>
                                <view class="right">
                                    题
                                </view>
                            </view>
                        </uni-td>
                    </uni-tr>
                    <uni-tr>
                        <uni-td align="center">总数</uni-td>
                        <uni-td align="center">共 <text class="blue_color"
                                style="padding: 0 .2rem;">{{all_question_num}}</text> 题
                        </uni-td>
                    </uni-tr>
                </uni-table>
            </view>

            <!-- 从考卷中的题目随机出题 -->
            <view v-if="question_rules_conf.self_or_public == 3 || (question_rules_conf.self_or_public != 3 && question_rules_conf.rand_question_types == 2)"
                class="question-item f-a-c">
                <view class="left">
                    题目数量：
                </view>
                <view class="right">
                    <uni-easyinput type="number" v-model="question_rules_conf.rand_question_num"
                        placeholder="随机出题的数量" />
                </view>
            </view>
        </view>

        <bottom-btn :btnList="btn_list" @save="save_random" @cancel="back_page"></bottom-btn>

    </view>
</template>

<script>
	import selectLay from "@/pages/likou_dati/uni_modules/select-lay/components/select-lay/select-lay.vue"
	import bottomBtn from "@/pages/likou_dati/components/bottom-btn/bottom-btn.vue"
	import uniTable from "@/pages/likou_dati/uni_modules/uni-table/components/uni-table/uni-table.vue"
	import uniTh from "@/pages/likou_dati/uni_modules/uni-table/components/uni-th/uni-th.vue"
	import uniTr from "@/pages/likou_dati/uni_modules/uni-table/components/uni-tr/uni-tr.vue"
	import uniTd from "@/pages/likou_dati/uni_modules/uni-table/components/uni-td/uni-td.vue"

    let app = getApp();
    export default {
		components: {
			selectLay,
			bottomBtn,
			uniTable,
			uniTh,
			uniTr,
			uniTd
		},
        data() {
            return {
                params: {},
                exam_details: {},

                btn_list: [{
                        text: '取消',
                        clickFun: 'cancel',
                        cancel: 1,
                    },
                    {
                        text: '保存',
                        clickFun: 'save'
                    }
                ],

                self_or_public: [{
                        label: '我的题库',
                        value: 1,
                    },
                    {
                        label: '公共题库',
                        value: 2,
                    },
                    {
                        label: '从已添加到考卷的题目里出题',
                        value: 3,
                    }
                ],

                category_list: [], // 题目来源
                category_idx: 0,

                rand_question_types: [{
                        label: '不限制题目类型，直接按照题目总数随机出题',
                        value: 2,
                    },
                    {
                        label: '设置不同题目类型不同数量',
                        value: 1,
                    }
                ],


                question_types_num: [{
                    title: '单选题',
                    question_types: 1,
                    num: 10,
                }, {
                    title: '多选题',
                    question_types: 2,
                    num: 10,
                }, {
                    title: '判断题',
                    question_types: 3,
                    num: 10,
                }, {
                    title: '填空题',
                    question_types: 4,
                    num: 10,
                }],

                all_question_num: 40,


                question_rules_conf: {
                    self_or_public: 1, // 题库拉题 1个人题库拉 2公共题库拉 3考卷中的题目随机
                    rand_question_types: 2, // 1指定题型 2不指定
                    rand_question_num: 10, // 不指定题型 从所有题型中拉取的题目数量
                    rand_category_id: '', // 拉取题目的题库分类指定  不指定就从个人题库或者公共题库拉取题目
                    single_answer: 10, // 单选题数量
                    multi_answer: 10, // 多选题数量
                    true_false: 10, // 判断题数量
                    fill_blank: 10, // 填空题数量
                },
            }
        },
        onLoad(options) {
            // console.log('获取的参数options', options);
            this.params = options;
            this.xwyLib.init(() => {
                this.init();
            })
        },
        methods: {
            async init() {
                this.xwyLib.setBarTitle('随机出题设置')
                this.userInfo = app.globalData.userInfo;
                this.get_exam_details();
            },

            // 获取考卷设置详情
            async get_exam_details() {
                let obj = {
                    exam_id: this.params.exam_id,
                    success: res => {
                        let conf = res.question_rules_conf;

                        // 随机选题指定的题目类型
                        if (conf?.rand_question_types && conf.rand_question_types == 1) {
                            if (conf.single_answer || conf.multi_answer || conf.true_false || conf
                                .fill_blank) {
                                this.question_types_num.forEach(val => {
                                    switch (Number(val.question_types)) {
                                        case 1:
                                            val.num = parseInt(conf.single_answer);
                                            break;
                                        case 2:
                                            val.num = parseInt(conf.multi_answer);
                                            break;
                                        case 3:
                                            val.num = parseInt(conf.true_false);
                                            break;
                                        case 4:
                                            val.num = parseInt(conf.fill_blank);
                                            break;
                                    }
                                })
                                this.question_rules_conf.rand_question_types = 2;
                                this.compute_num();
                            } else {
                                this.compute_num();
                            }
                        }

                        if (conf) this.question_rules_conf = conf;
                        this.exam_details = res;

                        this.get_category_list();
                    },
                    fail: res => {
                        this.xwyLib.showModal('未获取到考卷，即将返回到首页！', {
                            success: response => {
                                let url = `/pages/index/index`;
                                this.xwyLib.routeJump(url);
                            }
                        })
                    }
                }
                this.xwyLib.get_exam_papers_details(obj);
            },

            // 获取题库分类
            async get_category_list() {
                let opt = {
                    types: this.question_rules_conf.self_or_public == 2 ? 6 : 4, // 6公共题库分类 4我的考题分类
                    perpage: 100,
                    before: res => {
                        this.category_list = [];
                        this.category_idx = 0;
                    },
                    success: res => {
                        if (this.question_rules_conf.self_or_public == 1) {
                            let opt = {};
                            Object.assign(opt, res.data[0])
                            opt.name = '全部题库';
                            opt.label = opt.name;
                            opt.value = 0;
                            opt.category_id = '';
                            opt.sort_num = 1;
                            res.data.unshift(opt)
                        }

                        res.data.forEach((val, idx) => {
                            if (val.category_id == this.exam_details.rand_category_id) this
                                .category_idx = idx;
                            val.label = val.name;
                            val.value = idx
                        })
                        this.category_list = [...res.data];
                    },
                    fail: res => {
                        if (this.question_rules_conf.self_or_public == 1) {
                            let opt = {};
                            opt.name = '全部题库';
                            opt.category_id = '';
                            opt.sort_num = 1;
                            opt.label = opt.name;
                            opt.value = 0;
                            this.category_list = [opt];
                            return
                        }
                    }
                };

                if (this.question_rules_conf.self_or_public != 2) opt.my_self = 1
                this.xwyLib.get_category_list(opt);
            },

            // 选择我的题库还是公共题库或者从考卷中的题目拉题
            select_self_or_public(index, item) {
                console.log('index', index, item);
                if (index >= 0) {
                    this.question_rules_conf.self_or_public = item.value;
                    this.get_category_list();
                } else {
                    this.question_rules_conf.self_or_public = 1;
                }
            },

            // 选择题库分类
            select_category_list(index, item) {
                if (index >= 0) {
                    this.category_idx = item.value;
                    this.question_rules_conf.rand_category_id = this.category_list[this.category_idx].category_id
                } else {
                    this.category_idx = 0;
                }
            },

            // 选择题库出题规则
            select_rand_question_types(index, item) {
                if (index >= 0) {
                    this.question_rules_conf.rand_question_types = item.value;
                } else {
                    this.question_rules_conf.rand_question_types = 2;
                }
            },

            save_random() {
                this.save_random_set();
            },

            // 保存随机出题
            async save_random_set() {
                let opt = this.question_rules_conf;

                this.question_types_num.forEach(val => {
                    switch (Number(val.question_types)) {
                        case 1:
                            opt.single_answer = parseInt(val.num);
                            break;
                        case 2:
                            opt.multi_answer = parseInt(val.num);
                            break;
                        case 3:
                            opt.true_false = parseInt(val.num);
                            break;
                        case 4:
                            opt.fill_blank = parseInt(val.num);
                            break;
                    }
                })

                let question_rules_conf = {
                    ...opt,
                };

                let params = {
                    exam_id: this.params.exam_id,
                    is_rand_question: 1, // 随机出题 1随机出题 0固定出题
                    question_rules_conf: JSON.stringify(question_rules_conf),
                    success: res => {
                        this.xwyLib.showToast(res.info);
                        setTimeout(() => {
                            app.globalData.update_exam = 1;
                            this.back_page()
                        }, 1500)
                    }
                }
                this.xwyLib.exam_answer_conf_set(params)
            },


            // 返回上一页
            back_page() {
                this.xwyLib.back_previous_page();
            },

            // 改变题目
            change_question() {
                this.$nextTick(() => {
                    this.compute_num();
                })
            },

            compute_num() {
                let list = this.question_types_num,
                    num = 0;
                list.forEach((val, idx) => {
                    if (val.num) {
                        num = Number(num) + parseInt(val.num);
                    }
                })
                this.all_question_num = num;
            },

        }
    }
</script>
<style scoped lang='scss'>
	@import url('@/pages/likou_dati/common/css/common.scss');
	@import url('@/pages/likou_dati/common/css/iconfont.css');
	@import url('@/pages/likou_dati/common/css/public.css');

    .content {
        padding: 1rem;
        padding-bottom: 100px;

        .question-item {
            width: 100%;
            padding-bottom: 1rem;

            .left {
                width: 5.5rem;
            }

            .right {
                width: calc(100% - 5.5rem);

                input {
                    width: 100%;
                }
            }
        }

        .question-bank-box {
            width: 100%;

            .inp {
                width: 100%;

                .left {
                    width: calc(100% - 2rem);
                }

                .right {
                    min-width: 2rem;
                    width: 2rem;
                }
            }
        }

    }
</style>
