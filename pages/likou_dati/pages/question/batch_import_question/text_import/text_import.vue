<template>
    <view class='content'>
        <view class="container">
            <view class="title f-j-sb-a-c">
                <view class="left blue_color" @click.stop="assign_textarea_demo">
                    示例文本
                </view>
                <view class="right gray_color" @click.stop="clear_textarea">
                    清空文本
                </view>
            </view>
            <view class="textarea">
                <textarea v-model="textarea_value" maxlength="30000" placeholder="输入需要导入的题目" />
            </view>
            <!-- 说明 -->
            <view class="explain">
                <view class="title">
                    <text class="blue_color">说明：</text>
                    <text class="memo_text">（单次输入文本不超过30000字，请分段录入）</text>
                </view>
                <view class="text gray_color" v-html="explain_list[0].name" style="white-space: pre-wrap">
                </view>
                <view class="blue_color" @click.stop="assign_textarea_demo">
                    示例文本
                </view>
            </view>
        </view>

        <view class="tabbar-box f">
            <view class="right-btn f-j-a-c" style="border-right: 1px solid #eee;">
                <view class="btn f-j-a-c" @click.stop="back_page">
                    取消
                </view>
            </view>
            <view class="right-btn f-j-a-c">
                <view class="btn gre_color f-j-a-c" @click.stop="import_post">
                    导入预览
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    import importpost from "./textimport.js"
    let app = getApp();

    export default {
        data() {
            return {
                params: {},
                textarea_value: '',

                explain_list: [{
                    name: '1、第一行为题目，需要加题号（如：1. 或 1、）；\n' +
                        '2、答案选项：单选题和多选题需要填写选项；判断题不需要填写选项；\n' + // 和填空题
                        '单选题，多选题选项请按如下格式设置导入：\n' +
                        'A、选项1 \n' +
                        'B、选项2 \n' +
                        'C、选项3 \n' +
                        'D、选项4 \n' +
                        '3、正确答案：换行输入；\n' + '①单选题：答案：A;\n' + '②多选题：答案：A、B、C;\n' + '③判断题：答案：正确 或 错误;\n' +
                        '④填空题：需要多个空时用分号（;或；）隔开，一个空多个正确答案则用逗号（,或，）隔开; \n' +
                        '答案：空1答案；空2答案，空2第二个答案\n' +
                        '4、答题解析：换行输入 解析：，不输入则默认无解析 \n' +
                        '具体格式可以参考示例文本',
                    id: `001`
                }],
            }
        },
        onLoad(options) {
            // console.log('获取的参数options', options);
            this.params = options;
            this.xwyLib.init(() => {
                this.init();
            })
        },
        methods: {
            async init() {
                this.xwyLib.setBarTitle('文本批量导题');
            },

            // 预览题目
            assign_textarea_demo() {
                this.textarea_value =
                    '1、1919年，《新青年》杂志发表《我的马克思主义观》一文，系统地介绍了马克思主义的唯物史观、政治经济学和科学社会主义的基本原理。这篇文章的作者是\n' +
                    'A、陈独秀\n' +
                    'B、李大钊\n' +
                    'C、李达\n' +
                    'D、鲁迅\n' +
                    '答案：B\n' +
                    '解析：《我的马克思主义观》是李大钊于1919年在《新青年》上刊登的一篇文章。该文章论述了李大钊对于马克思主义的见解，极大推动了马克思主义在中国的传播。\n' +
                    '2、个人因与用人单位解除劳动关系而领取的一次性补偿收入时按照国家和地方政府规定的比例实际缴存的（ ），可以在计征其一次性补偿收入的个人所得税时予以扣除。\n' +
                    'A、住房公积金\n' +
                    'B、医疗保险费\n' +
                    'C、基本养老保险费\n' +
                    'D、失业保险费\n' +
                    '答案：A、B、C、D\n' +
                    '解析：本题考查解除与终止劳动合同的经济补偿。个人领取一次性补偿收入时按照国家和地方政府规定的比例实际缴纳的住房公积金、医疗保险费、基本养老保险费、失业保险费，可以在计征其一次性补偿收入的个人所得税时予以扣除。\n' +
                    '3、中国共产主义青年团成立年月是1922年5月。\n' +
                    '答案：正确\n' +
                    '解析：1921年7月中国共产党正式成立后，立即着手领导正式创建中国社会主义青年团。在中国共产党的直接领导和关怀下，1922年5月5日，中国社会主义青年团第一次全国代表大会在广州召开，标志中国青年团组织的正式成立。 \n' +
                    '4、一(_)一(_)、一日(_)\n' +
                    '答案：心；意；千里，三秋';
            },

            // 清空富文本
            clear_textarea() {
                this.textarea_value = '';
            },

            import_post() {
                let data = importpost.analytical(this.textarea_value);
                if (!Array.isArray(data) || !data.length) {
                    // this.xwyLib.alert('未获得解析后的题目，请按照规则填写！')
                    return
                }
                console.log('data',data);
                uni.setStorageSync('previewImport', JSON.stringify(data));
                let url = `/pages/likou_dati/pages/question/batch_import_question/preview_import/preview_import`;
                if (this.params.category_id) url += `?category_id=${this.params.category_id}`
                this.xwyLib.routeJump(url);
            },

            back_page() {
                this.xwyLib.back_previous_page();
            },
        }
    }
</script>
<style scoped lang='scss'>
	@import url('@/pages/likou_dati/common/css/common.scss');
	@import url('@/pages/likou_dati/common/css/iconfont.css');
	@import url('@/pages/likou_dati/common/css/public.css');
		
    .content {
        padding-bottom: 70px;

        .container {
            width: 100%;
            padding: 0 1rem;

            .title {
                width: 100%;

                >view {
                    padding: 1rem 0;
                }
            }

            .textarea {
                width: 100%;
                height: calc(100vh - 60px - 13rem);
                padding: 1rem;
                background-color: #f8f8f8;

                textarea {
                    width: 100%;
                    height: 100%;
                }
            }

            .explain {
                padding: 1rem 0;

                .title {
                    width: 100%;
                    padding: .5rem 0;
                }
            }

        }


        .tabbar-box {

            .right-btn {

                .btn {
                    width: 100%;
                    text-align: center;
                    font-size: 1rem;
                }
            }
        }
    }
</style>
