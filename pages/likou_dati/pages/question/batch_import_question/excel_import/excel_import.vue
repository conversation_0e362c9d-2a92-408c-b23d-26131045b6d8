<template>
    <view class='content'>
        <!-- 导入模板 -->
        <view v-if="excel_tpl" class="top-box">
            <view class="title">
                导入模板下载链接：
            </view>
            <view class="link-url">
                {{excel_tpl}}
            </view>
            <view class="copy-btn blue_color blue_bdrsolid" @click.stop="copy_excel_demo_url">
                复制链接到浏览器下载
            </view>
        </view>
        <!-- excel导入 -->
        <view class="excel-box pt1">
            <!-- <view class="title f-a-c">
					上传Excel文件
			</view> -->
            <view class="upload-excel gray_bdrsolid">
                <label class="f-d-c-j-a-c" @click.stop="change_excel">
                    <i class="iconfont gre_color">&#xe7af;</i>
                    <view class="memo_text">
                        {{loading?'导入中...':'上传文件'}}
                    </view>
                </label>
            </view>
            <!-- <view class="btn-box">
				<view class="btn blue_color blue_bdrsolid" @click.stop="upload">
					上传Excel
				</view>
			</view> -->
        </view>
    </view>
</template>

<script>
    let app = getApp();

    export default {
        data() {
            return {
                params: {},
                loading: false,

                excel_tpl: '',
                static_url: 'https://prod-0g479j60184f120d-1304148175.tcloudbaseapp.com/',
            }
        },
        onLoad(options) {
            // console.log('获取的参数options', options);
            this.params = options;
            this.xwyLib.init(() => {
                if (app.globalData?.shopconfset?.shop_set?.static_url) {
                    this.static_url = app.globalData?.shopconfset?.shop_set?.static_url;
                }
                this.init();
            })
        },
        methods: {
            async init() {
                this.xwyLib.setBarTitle('Excel批量导题');
                this.excel_tpl = `${this.static_url}web/wx-cloud-api/pages/active/public/exam_question_tpl.xlsx`
                this.excel_js();
            },

            // 复制excel模板url
            async copy_excel_demo_url() {
                this.xwyLib.copy_text({
                    url: this.excel_tpl,
                    text: `复制成功，请在浏览器打开下载！`,
                });
            },

            excel_js() {
                var script = document.createElement('script');

                let url = `${this.static_url}web/wx-cloud-api/lib/excel/xlsx.core.min.js`
                script.src = url;
                // script.src = "http://jx.hongsenlin8.com/xlsx.core.min.js";
                document.body.appendChild(script);
            },

            change_excel(e) {
                uni.chooseFile({
                    count: 1,
                    extension: ['.xlsx', '.xls'],
                    success: res => {
                        if (res.errMsg != 'chooseFile:ok') {
                            this.xwylib.alert('获取文件失败！')
                            return
                        }
                        let file = res.tempFiles[0];
                        this.upload(file)
                    }
                })
            },

            async upload(file) {
                this.xwyLib.showLoading('导入中...');
                this.loading = true;
                //获取到选中的文件
                if (file.size > 1024 * 1024) {
                    this.xwyLib.alert('当前文件大小:' + Math.floor(file.size / 1024) + 'KB,上传文件不能大于1024KB');
                    this.loading = false;
                    uni.hideLoading()
                    return false;
                }

                let reader = new FileReader();
                reader.readAsBinaryString(file);
                reader.onload = (e) => {
                    //------const  定义常量
                    const data = e.target.result;
                    const zzexcel = window.XLS.read(data, {
                        type: 'binary'
                    });
                    const result = [];
                    //----  输出结果
                    for (let i = 0; i < zzexcel.SheetNames.length; i++) {
                        const newData = window.XLS.utils.sheet_to_json(zzexcel.Sheets[zzexcel.SheetNames[i]]);
                        result.push(...newData)
                    };
                    this.get_data_from_excel(result);
                }
            },

            async get_data_from_excel(result) {
                let userInfo = app.globalData.userInfo,
                    new_data = {},
                    question_list = [];
                if (!this.xwyLib.isArray(result)) {
                    this.xwyLib.alert('没有解析出结果，请检查表格的内容！');
                    return
                }
                let list = result;
                for (let j = 0; j < list.length; j++) {
                    let item = list[j];
                    if (!item.题目) {
                        uni.hideLoading();
                        this.xwyLib.alert(`未解析出标题！---第${j+1}题`)
                        return
                    }
                    if (!item.正确答案) {
                        uni.hideLoading();
                        this.xwyLib.alert(`未解析出正确答案！---第${j+1}题`)
                        return
                    }

                    // 准备保存数据库在数组
                    new_data = {
                        "title": item.题目,
                        "explain_text": item.解题思路 || '',
                        "sort_num": item.排序 || 100,
                    }

                    let opt = this.judge_answer_options(item),
                        answer_option_list = opt.answer_option_list;
                    new_data.question_types = opt.question_types;

                    // 单选，多选
                    if (this.xwyLib.isArray(answer_option_list)) {
                        new_data.answer_option = answer_option_list
                    } else {
                        // 判断，填空
                        let str = this.trim_str(item.正确答案);
                        let judgeQuestion = 0;
                        switch (str) {
                            case '对':
                                judgeQuestion = 1
                                break;
                            case '错':
                                judgeQuestion = 2
                                break;
                            case '正确':
                                judgeQuestion = 1
                                break;
                            case '错误':
                                judgeQuestion = 2
                                break;
                            case '√':
                                judgeQuestion = 1
                                break;
                            case '×':
                                judgeQuestion = 2
                                break;
                        }

                        // 判断题目
                        if (judgeQuestion > 0) {
                            new_data.question_types = 3; // 判断题目
                            new_data.answer_option = [{
                                text: '正确',
                                is_right: judgeQuestion == 1 ? true : false,
                                num: '0'
                            }, {
                                text: '错误',
                                is_right: judgeQuestion == 2 ? true : false,
                                num: '1'
                            }]
                        } else {
                            new_data.question_types = 4; // 判断题目
                            let blank_list = str.split(/;|；/);
                            let answer_options = [];
                            if (this.xwyLib.isArray(blank_list)) {
                                blank_list.forEach((text, text_idx) => {
                                    if (text) {
                                        let t_list = [],
                                            bn_list = [];
                                        t_list = text.split(/，|,/);
                                        if (this.xwyLib.isArray(t_list)) {
                                            t_list.forEach(t => {
                                                bn_list.push({
                                                    text: t
                                                })
                                            })
                                        }
                                        answer_options.push({
                                            text: '',
                                            is_right: bn_list,
                                        })
                                    }
                                })
                            }
                            if (!this.xwyLib.isArray(answer_options)) {
                                xwyLib.alert(`第${i+1}题；${data[i].title}，答案出错!`)
                                return
                            }
                            new_data.answer_option = answer_options;
                        }
                    }
                    question_list.push(new_data);
                }

                uni.setStorageSync('previewImport', JSON.stringify(question_list));
                let url = `/pages/likou_dati/pages/question/batch_import_question/preview_import/preview_import`;
                if (this.params.category_id) url += `?category_id=${this.params.category_id}`
                this.xwyLib.routeJump(url);
                this.loading = false;
                return
                this.batch_save_post(question_list);
            },

            judge_answer_options(item) {
                // 候选答案数组
                let answer_option_list = [{
                        text: item.选项A,
                        is_right: false,
                        num: '0'
                    },
                    {
                        text: item.选项B,
                        is_right: false,
                        num: '1'
                    },
                    {
                        text: item.选项C,
                        is_right: false,
                        num: '2'
                    },
                    {
                        text: item.选项D,
                        is_right: false,
                        num: '3'
                    },
                    {
                        text: item.选项E,
                        is_right: false,
                        num: '4'
                    },
                    {
                        text: item.选项F,
                        is_right: false,
                        num: '5'
                    },
                ];

                let question_types = 1;

                // 把正确答案选出来
                if (item.正确答案 && item.正确答案.length > 1) {
                    question_types = 2 // 多选题目
                    var answer_list = item.正确答案.split(/[\.|、|．|。|,|，]/);
                    //多选题
                    for (var i = 0; i < answer_list.length; i++) {
                        switch (answer_list[i]) {
                            case 'A':
                                answer_option_list[0].is_right = true
                                break;
                            case 'B':
                                answer_option_list[1].is_right = true
                                break;
                            case 'C':
                                answer_option_list[2].is_right = true
                                break;
                            case 'D':
                                answer_option_list[3].is_right = true
                                break;
                            case 'E':
                                answer_option_list[4].is_right = true
                                break;
                            case 'F':
                                answer_option_list[5].is_right = true
                                break;
                            default:
                        }
                    }
                } else {
                    question_types = 1 // 单选题目
                    switch (item.正确答案) {
                        case 'A':
                            answer_option_list[0].is_right = true
                            break;
                        case 'B':
                            answer_option_list[1].is_right = true
                            break;
                        case 'C':
                            answer_option_list[2].is_right = true
                            break;
                        case 'D':
                            answer_option_list[3].is_right = true
                            break;
                        case 'E':
                            answer_option_list[4].is_right = true
                            break;
                        case 'F':
                            answer_option_list[5].is_right = true
                            break;
                    }
                }
                answer_option_list = this.clear_blank(answer_option_list)
                return {
                    answer_option_list,
                    question_types,
                }
            },

            // 如果 不为空的,就是需要使用的候选答案
            clear_blank(answer_option_list) {
                let new_option_list = []
                for (let item of answer_option_list) {
                    if (item.text != undefined && item.text) {
                        new_option_list.push(item)
                    }
                }
                return new_option_list
            },

            // 字符串删除换行 空格
            trim_str(str, type) {
                type = type || 1;
                switch (type) {
                    case 1:
                        return str.replace(/\s+/g, "");
                    case 2:
                        return str.replace(/(^\s*)|(\s*$)/g, "");
                    case 3:
                        return str.replace(/(^\s*)/g, "");
                    case 4:
                        return str.replace(/(\s*$)/g, "");
                    case 5:
                        return str.replace(/\n|\r/g, "");
                    case 5:
                        return str.replace(/&nbsp;/ig, "");
                    default:
                        return str;
                }
            },

            // 文本批量导入题目
            async batch_save_post(list = []) {
                if (!this.xwyLib.isArray(list)) {
                    this.xwyLib.alert('未获取到上传题目！');
                    return
                }
                this.xwyLib.showLoading();

                let params = {
                    question_list: this.xwyLib.jsonEncode(list),
                };

                let obj = {
                    data: params,
                    url: `front.flat.exam.questionBank/batch_export_question_bank`,
                    success: res => {
                        uni.hideLoading()
                        if (res.status != 1) {
                            this.xwyLib.alert(res.info);
                            return
                        }

                        uni.showModal({
                            content: res.info,
                            success: response => {
                                let url =
                                    `/pages/exam/question/question_bank_list/question_bank_list`;
                                if (this.params.category_id) url += `?public=1`
                                this.xwyLib.skip_page({
                                    types: 'closeUrl',
                                    url,
                                })
                            }
                        })
                    }
                };

                this.xwyLib.ajax(obj);
            },

        }
    }
</script>

<style scoped lang='scss'>
	@import url('@/pages/likou_dati/common/css/common.scss');
	@import url('@/pages/likou_dati/common/css/iconfont.css');
	@import url('@/pages/likou_dati/common/css/public.css');

    .content {
        padding: 0 1rem;

        .top-box {
            width: 100%;
            padding-bottom: 2rem;

            >view {
                padding: 1rem 0;
            }

            .link-url {
                padding: 1rem;
                background-color: #f8f8f8;
                border-radius: .2rem;
                word-break: break-all;
                margin-bottom: 1rem;
            }

            .copy-btn {
                width: 100%;
                border-radius: 10rem;
                padding: .7rem 0;
                text-align: center;
            }
        }

        .excel-box {
            width: 100%;

            .category-box {
                width: 100%;
                padding: 1rem 0;

                .left {
                    width: 6rem;
                }

                .right {
                    width: calc(100% - 6rem);
                }
            }

            .title {
                padding: 1rem 0;
            }

            .upload-excel {
                width: 6rem;
                height: 6rem;
                border-radius: .3rem;
                margin-bottom: 1rem;

                >label {
                    width: 100%;
                    height: 100%;

                    .iconfont {
                        font-size: 2.2rem;
                        padding-bottom: .3rem;
                    }
                }
            }

            .btn-box {
                width: 100%;

                .btn {

                    padding: .7rem 0;
                    border-radius: 10rem;
                    text-align: center;
                }
            }
        }
    }
</style>
