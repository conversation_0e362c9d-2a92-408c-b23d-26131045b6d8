<template>
    <!--
        抓汤圆游戏主页面
        说明：
        - 仅 MP-WEIXIN 使用
        - 使用普通节点与 CSS 动画实现移动与交互
        - 入碗后汤圆保留在碗内并冻结，不再可拖拽
    -->
    <view class="page" catchtouchmove>
        <view class="game-tips-msg-bar">
            <game-top-msg-bar>
                <template v-slot:leftMsg>{{ remainSecDisplay }}</template>
                <template v-slot:rightMsg>{{ score }}</template>
            </game-top-msg-bar>
        </view>

        <game-tips-popup ref="gameTipsPopup" :show-ad="showAD" @startGame="gameTipsClose">
            <view class="game-tips">
                <view>
                    <text>请用手指按住移动的</text>
                    <image :src="assets.tangyuan" mode="heightFix"/>
                    <text>并拖到碗中。</text>
                </view>
                <view>
                    <text>游戏倒计时{{ config.durationSec }}秒结束后结算{{ unit }}，每抓住1个</text>
                    <image :src="assets.tangyuan" mode="heightFix"/>
                    <text>可获得{{ per_integral }}{{ unit }}。</text>
                </view>
            </view>
        </game-tips-popup>

        <ready-countdown ref="readyCountdown" @countdownOver="countdownOver"/>

        <game-result-popup ref="resultPopup" :show-ad="showAD" :unit="unit"/>

        <!-- 游戏区域（相对定位容器） -->
        <view class="game-area" id="gameArea">
            <!-- 背景图 -->
            <image class="bg" :src="assets.bg" mode="widthFix"></image>

            <!-- 汤圆节点列表 -->
            <view
                v-for="item in tangyuanList"
                :key="item.id"
                class="tangyuan"
                :style="{ transform: 'translate3d(' + item.x + 'px,' + item.y + 'px,0)', width: item.size + 'px', height: item.size + 'px' }"
                :data-id="item.id"
                @touchstart.stop.prevent="onTangyuanTouchStart"
                @touchmove.stop.prevent="onTangyuanTouchMove"
                @touchend.stop.prevent="onTangyuanTouchEnd"
            >
                <view
                    class="tangyuan__inner"
                    :class="{ 'is-moving': item.state === 'moving', 'is-bounce': item.justScored, 'is-fading': item.state === 'fading' }"
                    :style="item.state === 'moving' ? ('animation-duration:' + item.animDurationSec + 's; --move-distance:' + (-item.animDistance) + 'px;') : ''"
                    @animationend="onMoveAnimationEnd(item)"
                >
                    <image class="tangyuan__img" :src="assets.tangyuan" mode="aspectFill"></image>
                </view>
            </view>

            <!-- 分数飘字（以碗上方为基准） -->
            <view v-if="scoreFloat.visible" class="score-float" :style="scoreFloatStyle">+1</view>

            <!-- 碗（固定底部，宽=屏宽） -->
            <image id="bowl" class="bowl" :src="assets.bowl" mode="widthFix" @load="onBowlLoad"></image>
        </view>
    </view>
</template>

<script>
import gameTipsPopup from '../components/game-tips-popup.vue'
import gameTopMsgBar from '../components/game-top-msg-bar.vue'
import readyCountdown from '../components/ready-countdown.vue'

// 工具：简单的唯一 id 生成（时间戳 + 自增）
let idSeq = 0
const BASE_URL = 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/game/tangyuan/'

export default {
    components: {gameTipsPopup, gameTopMsgBar, readyCountdown},
    data() {
        return {
            // 资源地址（云端）
            assets: {
                bg: `${BASE_URL}bg.jpg`,
                tangyuan: `${BASE_URL}tangyuan.png`,
                bowl: `${BASE_URL}bowl.png`
            },

            // 可调配置（详见 game.md）
            config: {
                durationSec: 30,
                initialSpawnIntervalMs: 1200,
                minSpawnIntervalMs: 500,
                initialSpeedPxPerSec: 160,
                maxSpeedPxPerSec: 520,
                speedCurveGamma: 1.6,
                maxConcurrentItems: 8,
                overlapThreshold: 0.6,
                tangyuanRenderSizePx: 80,
                spawnEndGuardPx: 36
            },

            // 运行状态
            isRunning: false,
            startTimestamp: 0,               // 开始时间戳（ms）
            remainSec: 30,                    // 剩余秒数
            score: 0,                         // 当前分数

            // 几何与环境信息
            windowWidth: 375,
            windowHeight: 667,
            bowlRect: null,                   // {left, top, width, height}
            // bowlReady: false,

            // 汤圆列表
            tangyuanList: [],                 // 数组元素结构见下方注释

            // 计时器句柄
            spawnTimer: null,
            countdownTimer: null,

            // 飘字控制（以碗上方为基准）
            scoreFloat: {
                visible: false
            },

            // touchmove 节流
            lastTouchMoveTs: 0,

            per_integral: 0,
            unit: '积分',
            showAD: false
        }
    },

    computed: {
        // 展示用的剩余时间字符串（整数）
        remainSecDisplay() {
            return Math.max(0, Math.ceil(this.remainSec))
        },

        // 飘字定位样式
        scoreFloatStyle() {
            if (!this.bowlRect) return {}
            const x = this.bowlRect.left + this.bowlRect.width / 2
            const y = Math.max(0, this.bowlRect.top - 24)
            return `left: ${x}px; top: ${y}px;`
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.point_id = params.point_id
        this.unit = params.unit
        this.per_integral = Number(params.per_integral)
        this.config.durationSec = Number(params.seconds)
        this.remainSec = this.config.durationSec
        if (params.show_ad) this.showAD = true
        if (params.title) this.$uni.setNavigationBarTitle(params.title)

        this.initGame()
    },

    /*onReady() {
        // 等待碗图片加载完成后开始游戏
        // 若碗已加载，直接启动
        if (this.bowlReady) {
            this.startGame()
        }
    },*/

    onUnload() {
        // 清理计时器，避免内存泄漏
        this.clearAllTimers()
    },
    onHide() {
        // 页面进入后台时也清理计时器，符合“不可暂停”要求
        this.clearAllTimers()
        this.isRunning = false
    },

    methods: {
        async initGame() {
            // 获取窗口尺寸
            const {windowWidth, windowHeight} = uni.getWindowInfo()
            this.windowWidth = windowWidth
            this.windowHeight = windowHeight

            await this.getGameData()

            this.$refs.gameTipsPopup.open()
        },

        async getGameData() {
            const data = await this.getOpenerEventChannelData() || {}

            if (data.bg_img) this.assets.bg = data.bg_img

            if (data.navigation_bar) uni.setNavigationBarColor({
                ...data.navigation_bar,
                fail: err => console.log(err)
            })
        },

        getOpenerEventChannelData() {
            return new Promise(resolve => {
                if (!this.getOpenerEventChannel?.()?.once) resolve({})
                this.getOpenerEventChannel().once('data', data => resolve(data))
            })
        },

        gameTipsClose() {
            this.$refs.gameTipsPopup.close()
            this.$refs.readyCountdown.open()
        },

        countdownOver() {
            this.startGame()
        },

        // 启动游戏：重置状态 + 启动倒计时与生成器
        startGame() {
            if (this.isRunning) return
            this.isRunning = true
            this.score = 0
            this.tangyuanList = []
            this.startTimestamp = Date.now()
            this.remainSec = this.config.durationSec

            // 倒计时（高精度基于时间戳计算）
            this.countdownTimer = setInterval(() => {
                const elapsed = (Date.now() - this.startTimestamp) / 1000
                const remain = this.config.durationSec - elapsed
                this.remainSec = remain
                if (remain <= 0) {
                    this.onGameOver()
                }
            }, 100)

            // 开始生成汤圆
            this.scheduleNextSpawn()
        },

        // 结束游戏：停止生成，展示结果
        onGameOver() {
            if (!this.isRunning) return
            this.isRunning = false
            this.clearAllTimers()
            this.remainSec = 0

            this.submitResult()
        },

        async submitResult() {
            const count = this.score
            const sign = {
                types: 47,
                point_id: this.point_id,
                result: count ? 'success' : 'fail',
                count
            }

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify(sign))
                }
            })

            let resultCode = sign.result === 'success' ? 1 : 0
            let info = resultCode === 1 ? `共抓到${count}个` : '任务失败'
            if (res?.status !== 1) {
                resultCode = 0
                if (res?.info) info = res.info
            }

            this.$refs.resultPopup.open({
                code: resultCode,
                integral: res.data.num || null,
                info
            })


            this.getOpenerEventChannel().emit('success')
        },

        // 清除所有计时器
        clearAllTimers() {
            if (this.spawnTimer) {
                clearTimeout(this.spawnTimer)
                this.spawnTimer = null
            }
            if (this.countdownTimer) {
                clearInterval(this.countdownTimer)
                this.countdownTimer = null
            }
        },

        // 根据进度计算当前速度（线性）
        currentSpeedPxPerSec() {
            const progress = this.gameProgress()
            const {initialSpeedPxPerSec, maxSpeedPxPerSec, speedCurveGamma} = this.config
            const gamma = typeof speedCurveGamma === 'number' ? Math.max(1, speedCurveGamma) : 1
            const shaped = Math.pow(progress, gamma)
            return initialSpeedPxPerSec + (maxSpeedPxPerSec - initialSpeedPxPerSec) * shaped
        },

        // 根据进度计算生成间隔（线性衰减）
        currentSpawnIntervalMs() {
            const progress = this.gameProgress()
            const {initialSpawnIntervalMs, minSpawnIntervalMs} = this.config
            const interval = initialSpawnIntervalMs + (minSpawnIntervalMs - initialSpawnIntervalMs) * progress
            return Math.max(minSpawnIntervalMs, Math.floor(interval))
        },

        // 游戏进度 [0,1]
        gameProgress() {
            if (!this.isRunning) return 0
            const elapsed = (Date.now() - this.startTimestamp) / 1000
            const duration = this.config.durationSec
            return Math.min(1, Math.max(0, elapsed / duration))
        },

        // 计划下一次生成
        scheduleNextSpawn() {
            if (!this.isRunning) return

            const interval = this.currentSpawnIntervalMs()
            this.spawnTimer = setTimeout(() => {
                this.trySpawnTangyuan()
                this.scheduleNextSpawn()
            }, interval)
        },

        // 生成汤圆（控制活跃数量）
        trySpawnTangyuan() {
            if (!this.bowlRect) return
            // 活跃（可移动或可交互）的数量
            const activeCount = this.tangyuanList.filter(t => t.state === 'moving' || t.state === 'dragging').length
            if (activeCount >= this.config.maxConcurrentItems) return

            const size = this.config.tangyuanRenderSizePx
            const startX = this.windowWidth + size

            // 可生成的 Y 轴范围（避免与碗重叠）
            // 固定顶部边距，避免与 topbar 重叠（topbar 高度约 48px）
            const topbarHeight = 48
            const yMin = Math.max(this.windowHeight * 0.2, topbarHeight)
            const baseGuard = typeof this.config.spawnEndGuardPx === 'number' ? this.config.spawnEndGuardPx : 12
            const effGuard = Math.max(baseGuard, size * 0.75, this.windowHeight * 0.04)
            const yMax = Math.max(yMin + 1, this.bowlRect.top - effGuard - size)
            const y = Math.floor(yMin + Math.random() * (yMax - yMin))

            const speed = this.currentSpeedPxPerSec()
            const distance = startX + size // 从当前位置到完全越界左侧的位移
            const durationSec = distance / speed

            const item = {
                id: `ty_${Date.now()}_${idSeq++}`,
                x: startX,
                y,
                size,
                speed,
                state: 'moving',
                spawnTime: Date.now(),
                animDistance: distance,
                animDurationSec: durationSec,
                // 拖拽时用到的偏移
                dragDx: 0,
                dragDy: 0
            }

            this.tangyuanList.push(item)
        },


        // 碗图片加载后，读取其几何信息
        onBowlLoad() {
            this.$nextTick(() => {
                uni.createSelectorQuery().in(this).select('#bowl').boundingClientRect(rect => {
                    if (rect) {
                        this.bowlRect = rect
                        /*this.bowlReady = true
                        // 如果 ready 时页面已 onReady，则启动游戏
                        if (!this.isRunning && this.remainSec === this.config.durationSec) {
                            this.startGame()
                        }*/
                    }
                }).exec()
            })
        },

        // 计算是否进入碗：使用 AABB 交叠最小阈值，允许部分超出碗底
        isInBowl(item) {
            if (!this.bowlRect) return false
            const itemLeft = item.x
            const itemTop = item.y
            const itemRight = item.x + item.size
            const itemBottom = item.y + item.size
            const bowlLeft = this.bowlRect.left
            const bowlTop = this.bowlRect.top
            const bowlRight = this.bowlRect.left + this.bowlRect.width
            const bowlBottom = this.bowlRect.top + this.bowlRect.height
            const overlapW = Math.min(itemRight, bowlRight) - Math.max(itemLeft, bowlLeft)
            const overlapH = Math.min(itemBottom, bowlBottom) - Math.max(itemTop, bowlTop)
            const minOverlap = item.size * 0.2 // 至少有 20% 尺寸的重叠
            return overlapW >= minOverlap && overlapH >= minOverlap
        },

        // 动画结束（越界），视为未入碗，回收节点
        onMoveAnimationEnd(item) {
            if (item.state === 'moving') {
                // 先触发 120ms 淡出，再移除
                item.state = 'fading'
                setTimeout(() => {
                    const idx = this.tangyuanList.findIndex(t => t.id === item.id)
                    if (idx !== -1) this.tangyuanList.splice(idx, 1)
                }, 120)
                return
            }
        },

        // 触摸开始：命中即进入拖拽态
        onTangyuanTouchStart(e) {
            if (!this.isRunning) return
            const id = e.currentTarget.dataset.id
            const item = this.tangyuanList.find(t => t.id === id)
            if (!item) return
            if (item.state === 'inBowl') return

            // 计算手指相对元素左上角偏移，便于跟随
            const touch = e.touches[0]
            item.state = 'dragging'
            // 当前内层动画需要被停止：将剩余距离固化到 x 上
            // 方案：当进入拖拽时，内层 transform 归零，把外层定位更新为当前视觉位置
            // 近似处理：根据手指位置直接放置
            const targetX = touch.pageX - item.size / 2
            const targetY = touch.pageY - item.size / 2
            item.dragDx = touch.pageX - targetX
            item.dragDy = touch.pageY - targetY
            item.x = targetX
            item.y = targetY
        },

        // 触摸移动：节流到约 16–32ms
        onTangyuanTouchMove(e) {
            const now = Date.now()
            if (now - this.lastTouchMoveTs < 16) return
            this.lastTouchMoveTs = now

            const id = e.currentTarget.dataset.id
            const item = this.tangyuanList.find(t => t.id === id)
            if (!item || item.state !== 'dragging') return
            const touch = e.touches[0]

            // 计算跟随坐标（限制边界到容器内）
            const size = item.size
            let nx = touch.pageX - size / 2
            let ny = touch.pageY - size / 2

            nx = Math.max(-size, Math.min(this.windowWidth, nx))
            ny = Math.max(0, Math.min(this.windowHeight - size, ny))

            item.x = nx
            item.y = ny
        },

        // 触摸结束：判定是否入碗
        onTangyuanTouchEnd(e) {
            if (!this.isRunning) return
            const id = e.currentTarget.dataset.id
            const item = this.tangyuanList.find(t => t.id === id)
            if (!item || item.state !== 'dragging') return

            const inBowl = this.isInBowl(item)
            if (inBowl) {
                // 计分 + 动效（入碗缩放 + 碗上方飘字）
                item.state = 'inBowl'
                // 将其位置钳制到碗内，允许少量重叠但不越出碗底
                const clampY = Math.min(item.y, this.bowlRect.top + this.bowlRect.height - item.size)
                item.y = Math.max(this.bowlRect.top + 4, clampY)
                // 同时限制 X 保持在碗的左右边界内
                const minX = this.bowlRect.left
                const maxX = this.bowlRect.left + this.bowlRect.width - item.size
                item.x = Math.max(minX, Math.min(maxX, item.x))
                // 得分
                this.score += 1
                this.playInBowlFeedback()
                // 入碗缩放一次性动画
                item.justScored = true
                setTimeout(() => {
                    item.justScored = false
                }, 120)
            } else {
                // 失败：继续向左运动
                const size = item.size
                const distance = item.x + size
                const speed = this.currentSpeedPxPerSec()
                const durationSec = Math.max(0.2, distance / speed)
                item.state = 'moving'
                item.animDistance = distance
                item.animDurationSec = durationSec

                // 为了触发重新动画，需要强制刷新（改变 key 或使用小延时）
                // 这里通过微调 spawnTime 触发样式更新
                item.spawnTime = Date.now()
            }
        },

        // 入碗反馈：缩放 + 飘字
        playInBowlFeedback() {
            // 飘字 300ms
            this.scoreFloat.visible = true
            setTimeout(() => {
                this.scoreFloat.visible = false
            }, 300)
        }

    }
}
</script>

<style lang="scss" scoped>
/* 页面基础样式，禁止滚动 */
.page {
    position: relative;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    background: #000
}

.game-tips-msg-bar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
    width: 100%;
}

.game-tips {
    padding: 0 20px;
    line-height: 30px;

    image {
        height: 30px;
        vertical-align: text-bottom;
    }
}

.game-area {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 1;
}

.bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 0
}

.bowl {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    z-index: 1
}

/* 汤圆外层：负责定位（transform: translate3d(x,y,0)） */
.tangyuan {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 2;
    will-change: transform
}

/* 汤圆内层：负责横向移动动画 */
.tangyuan__inner {
    width: 100%;
    height: 100%;
    transform: translate3d(0, 0, 0);
}

.tangyuan__inner.is-moving {
    animation-name: move-left;
    animation-timing-function: linear;
    animation-fill-mode: both
}

.tangyuan__inner.is-bounce {
    animation: bounce-quick .12s ease-out both
}

.tangyuan__inner.is-fading {
    animation: fade-out .12s linear both
}

.tangyuan__img {
    width: 100%;
    height: 100%;
    display: block;
}

/* 横向移动关键帧：使用 CSS 变量 --move-distance 控制位移 */
@keyframes move-left {
    from {
        transform: translate3d(0, 0, 0)
    }
    to {
        transform: translate3d(var(--move-distance), 0, 0)
    }
}

@keyframes bounce-quick {
    0% {
        transform: scale(1)
    }
    60% {
        transform: scale(1.1)
    }
    100% {
        transform: scale(1)
    }
}

@keyframes fade-out {
    from {
        opacity: 1
    }
    to {
        opacity: 0
    }
}

/* 分数飘字（碗上方固定） */
.score-float {
    position: absolute;
    z-index: 3;
    color: #fffbcc;
    font-size: 20px;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, .35);
    animation: float-up .3s ease-out both;
    transform: translate3d(-50%, 0, 0) /* 以 left 为中心点对齐 */
}

@keyframes float-up {
    from {
        opacity: 1;
        transform: translate3d(-50%, 0, 0)
    }
    to {
        opacity: 0;
        transform: translate3d(-50%, -24px, 0)
    }
}
</style>