<template>
    <view>
        <roll-dice :allow-roll="allowRoll" @rollOver="rollOver"/>
        <game-tips-popup ref="gameTipsPopup" :tips-list="tipsList" :show-ad="showAD" @startGame="startGame">
            <slot v-if="boBingSet.reward_type === 2">
                <view class="text-center p10" @click="lookRewardRules">
                    <text class="font14 color-light-primary">查看奖励规则</text>
                    <uni-icons type="forward" size="14" color="#5cadff"/>
                </view>
            </slot>
        </game-tips-popup>
        <game-result-popup ref="resultPopup" :show-ad="showAD" :unit="unit"/>
    </view>
</template>

<script>
import gameTipsPopup from '../components/game-tips-popup.vue'
import rollDice from './roll-dice.vue'

export default {
    components: {gameTipsPopup, rollDice},
    data() {
        return {
            allowRoll: false,
            per_integral: 0,
            unit: '积分',
            showAD: false,
            diceValues: [0, 0, 0, 0, 0, 0],
            boBingSet: {}
        }
    },

    computed: {
        tipsList() {
            const tipsList = ['🎲 同时掷出六枚神奇骰子']

            const reward_type = this.boBingSet?.reward_type || 1
            if (reward_type === 1) {
                tipsList.push(`🎯 骰子停止滚动后计算点数总和，每一点可获得${this.per_integral}${this.unit}`)
            } else if (reward_type === 2) {
                tipsList.push(`🎯 骰子停止滚动后，根据摇出的骰子组合，获得对应的${this.unit}奖励`)
            }

            return tipsList
        },

        diceTotalCount() {
            return this.diceValues.reduce((acc, val) => acc + val, 0)
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.point_id = params.point_id
        this.per_integral = params.per_integral
        this.unit = params.unit
        if (params.show_ad) this.showAD = true
        if (params.title) this.$uni.setNavigationBarTitle(params.title)

        this.init()
    },

    methods: {
        async init() {
            this.$uni.showLoading()
            await this.getBoBingSet()
            uni.hideLoading()

            this.$refs.gameTipsPopup.open()
        },

        async getBoBingSet() {
            const data = await this.getOpenerEventChannelData() || {reward_type: 1, reward_rules: []}
            this.boBingSet = data
        },

        getOpenerEventChannelData() {
            return new Promise(resolve => this.getOpenerEventChannel().once('data', data => resolve(data)))
        },


        startGame() {
            this.$refs.gameTipsPopup.close()
            this.allowRoll = true
        },

        rollOver(values) {
            this.diceValues = values
            this.allowRoll = false

            // 延迟1秒，让用户看下自己摇的骰子
            setTimeout(() => this.$nextTick(() => this.submitResult()), 1000)
        },

        async submitResult() {
            let count = this.diceTotalCount
            let resultInfo = `骰子点数总和为${count}点`

            if (this.boBingSet.reward_type === 2) {
                const rule = this.getBoBingRewardIntegral()
                count = rule?.integral || 0
                resultInfo = rule ? `获得 ${rule.name || ''}` : '任务失败'
            }


            const sign = {
                types: 46,
                point_id: this.point_id,
                result: count ? 'success' : 'fail',
                count
            }

            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify(sign))
                }
            })
            uni.hideLoading()

            let resultCode = sign.result === 'success' ? 1 : 0
            let info = resultCode === 1 ? resultInfo : '任务失败'
            if (res?.status !== 1) {
                resultCode = 0
                if (res?.info) info = res.info
            }

            this.$refs.resultPopup.open({
                code: resultCode,
                integral: res.data.num || null,
                info
            })


            this.getOpenerEventChannel().emit('success')
        },

        // 博饼模式下，根据骰子点数计算奖励积分
        getBoBingRewardIntegral() {
            const dices = this.diceValues

            // 如果骰子数据无效，返回0积分
            if (!dices || dices.length !== 6) return false

            // 对骰子点数进行排序，便于匹配
            const sortedDices = [...dices].sort((a, b) => a - b)

            // 存储所有匹配的规则及其积分
            const matchedRules = []

            // 遍历所有博饼奖励规则
            for (const rule of this.boBingSet.reward_rules) {
                if (this.isRuleMatched(sortedDices, rule.dice_values)) {
                    matchedRules.push({
                        name: rule.name,
                        integral: rule.integral,
                        dice_values: rule.dice_values
                    })
                }
            }

            // 如果没有匹配到任何规则，返回0积分
            if (matchedRules.length === 0) return false

            // 如果匹配到多条规则，选择积分最高的那条
            const bestRule = matchedRules.reduce((max, current) => {
                return current.integral > max.integral ? current : max
            })

            return {
                name: bestRule.name,
                integral: bestRule.integral
            }
        },

        // 检查骰子组合是否匹配指定的规则
        isRuleMatched(sortedDices, rulePattern) {
            // 对规则模式也进行排序
            const sortedPattern = [...rulePattern].sort((a, b) => a - b)

            // 如果是完全匹配的规则（如六杯红、插金花等）
            if (sortedPattern.length === 6) {
                return this.isExactMatch(sortedDices, sortedPattern)
            }

            // 如果是部分匹配的规则（如一秀、二举等）
            return this.isPartialMatch(sortedDices, sortedPattern)
        },

        // 检查是否完全匹配（用于6个骰子的固定组合）
        isExactMatch(sortedDices, sortedPattern) {
            if (sortedDices.length !== sortedPattern.length) {
                return false
            }

            // 逐个比较排序后的数组
            for (let i = 0; i < sortedDices.length; i++) {
                if (sortedDices[i] !== sortedPattern[i]) {
                    return false
                }
            }
            return true
        },

        // 检查是否部分匹配（用于少于6个骰子的规则，如一秀、二举等）
        isPartialMatch(sortedDices, sortedPattern) {
            // 统计骰子中每个点数的出现次数
            const diceCount = {}
            for (const dice of sortedDices) {
                diceCount[dice] = (diceCount[dice] || 0) + 1
            }

            // 统计规则模式中每个点数需要的次数
            const patternCount = {}
            for (const point of sortedPattern) {
                patternCount[point] = (patternCount[point] || 0) + 1
            }

            // 检查骰子中是否包含足够数量的指定点数
            for (const [point, requiredCount] of Object.entries(patternCount)) {
                const actualCount = diceCount[point] || 0
                if (actualCount < requiredCount) {
                    return false
                }
            }

            return true
        },

        lookRewardRules() {
            const url = `/pages/games/roll-dice/bo-bing-reward-rules-set?unit=${this.unit}&just_look=1`
            this.$uni.navigateTo(url, {
                success: res => res.eventChannel.emit('rewardRules', this.boBingSet.reward_rules || [])
            })
        }
    },
}
</script>

<style scoped lang="scss">

</style>
