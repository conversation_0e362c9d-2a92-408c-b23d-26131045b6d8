/*
 * @Author: chliangck <EMAIL>
 * @LastEditors: chliangck <EMAIL>
 * @LastEditTime: 2025-08-14 14:42:40
 * @FilePath: /线上活动王/pages/game/roll-dice/config.js
 * @Description: 掷骰子游戏配置文件
 */

const baseUrl = 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/game/roll-dice/'

// 骰子相关图片 - 生成 dice1 到 dice6 的图片路径
// 最终生成：{ dice1: 'baseUrl/dice/1.png', dice2: 'baseUrl/dice/2.png', ..., dice6: 'baseUrl/dice/6.png' }
const diceImages = Object.fromEntries(
    Array.from({ length: 6 }, (_, i) => [`dice${i + 1}`, `${baseUrl}dice/${i + 1}.png`])
);

export default {
    images: {
        // 背景图片
        bgImg: `${baseUrl}bg.png`,
        bowl: `${baseUrl}bowl.png`,
        rollBtnImg: `${baseUrl}button.png`,
        
        // 骰子相关图片
        ...diceImages,
    },
    
    // 生成6个骰子的初始配置，2行3列布局
    diceList: Array.from({length: 6}, (_, index) => ({
        value: 0,        // 获得骰子点数
        rolling: false,  // 骰子是否正在滚动
        left: 25 + (index % 3) * 10,  // 列：25, 35, 45
        top: 40 + Math.floor(index / 3) * 20,  // 行：40, 60
    })),
    
    // 物理引擎参数
    physicsConfig: {
        gravity: 0.3,
        friction: 0.98,
        bounce: 0.7,
        bowlRadius: 40,
        diceSize: 15,
        animationDuration: 3000,
    },
}
