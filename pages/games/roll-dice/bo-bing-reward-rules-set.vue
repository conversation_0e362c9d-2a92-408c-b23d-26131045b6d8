<template>
    <view class="page">
        <view v-if="!justLook" class="rules-count-add-role flex-kai font14">
            <view class="color-sub p10">已设置{{ rewardRules.length }}组奖励规则</view>
            <view class="p10">
                <text class="color-light-primary" @click="addRule">添加规则</text>
                <uni-icons type="forward" size="14" color="#5cadff"/>
            </view>
        </view>
        <view class="rules">
            <view class="rule-item" v-for="(item, index) in rewardRules" :key="item.key">
                <view>{{ item.name }}</view>
                <view class="dice-value-images flex-row">
                    <view v-for="(dice, i) in item.dice_values" :key="i" class="dice-image">
                        <image class="dice-value-img" :src="diceImages[`dice${dice}`]" mode="aspectFill"/>
                    </view>
                </view>
                <view class="flex-kai">
                    <view class="color-content">获得{{ item.integral || 0 }}{{ unit }}</view>
                    <view v-if="!justLook" class="item-controls flex-row">
                        <view class="p5" @click="editRule(index)">
                            <text class="iconfont icon-edit color-sub font18"></text>
                        </view>
                        <view class="p5" @click="deleteRule(index)">
                            <text class="iconfont icon-delete color-sub font18"></text>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <uni-popup ref="editPopup">
            <view class="edit-popup">
                <view class="text-center">编辑规则</view>
                <uni-forms label-position="top" label-width="300">
                    <uni-forms-item label="规则名称">
                        <uni-easyinput v-model="editData.name" :maxlength="20"/>
                    </uni-forms-item>
                    <uni-forms-item label="骰子组合">
                        <view class="form-description color-sub font12">
                            请填写1-6位1-6的整数，每一位代表1个骰子的点数。
                        </view>
                        <uni-easyinput type="number" v-model="editData.dice_values" :maxlength="6"/>
                    </uni-forms-item>
                    <uni-forms-item :label="`奖励${unit}`">
                        <uni-easyinput type="digit" v-model="editData.integral" :maxlength="8"/>
                    </uni-forms-item>
                </uni-forms>
                <view class="flex-all-center">
                    <view class="confirm-button bg-light-primary color-white text-center" @click="confirm">
                        确定
                    </view>
                </view>
                <view class="flex-all-center">
                    <view class="p10 color-sub font14" @click="$refs.editPopup.close()">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import config from '@/pages/games/roll-dice/config.js'

export default {
    data() {
        return {
            rewardRules: [],
            diceImages: {
                dice1: config.images.dice1,
                dice2: config.images.dice2,
                dice3: config.images.dice3,
                dice4: config.images.dice4,
                dice5: config.images.dice5,
                dice6: config.images.dice6
            },
            unit: '积分',

            editData: {
                name: '',
                dice_values: '',
                integral: ''
            },

            justLook: false
        }
    },

    onLoad(params) {
        if (params.just_look) {
            this.justLook = true
            this.$uni.setNavigationBarTitle('奖励规则')
        }
        if (params.unit) this.unit = params.unit
        this.init()
    },

    methods: {
        async init() {
            this.$uni.showLoading()
            this.rewardRules = await this.getActiveRulesSet()
            uni.hideLoading()
        },

        async getActiveRulesSet() {
            const rules = await this.getOpenerEventChannelData() || []
            return rules.map(item => ({
                ...item,
                key: item.dice_values.join('')
            }))

        },

        getOpenerEventChannelData() {
            return new Promise(resolve => this.getOpenerEventChannel().once('rewardRules', rules => resolve(rules)))
        },

        addRule() {
            this.editIndex = null
            const editData = {
                name: '',
                dice_values: '',
                integral: ''
            }
            this.editPopupOpen(editData)
        },

        editRule(index) {
            this.editIndex = index
            const data = JSON.parse(JSON.stringify(this.rewardRules[index]))
            const editData = {
                name: data.name,
                dice_values: data.dice_values.join(''),
                integral: data.integral || ''
            }
            this.editPopupOpen(editData)
        },

        editPopupOpen(editData) {
            this.editData = editData
            this.$nextTick(() => {
                this.$refs.editPopup.open()
            })
        },

        confirm() {
            const {name, dice_values, integral} = this.editData
            if (!name.trim()) {
                this.$uni.showToast('请输入规则名称')
                return
            }

            const diceValues = dice_values.split('').map(Number)
            if (!this.diceValuesCheck(diceValues)) return
            if (this.diceValuesRepeatCheck(diceValues)) {
                this.$uni.showToast('该骰子组合已存在')
                return
            }

            let integralNum = Number(integral)
            if (isNaN(integralNum) || integralNum < 0) {
                this.$uni.showToast(`请输入正确的奖励${this.unit}`)
                return
            }


            const item = {
                name,
                dice_values: diceValues,
                integral: integralNum
            }

            if (this.editIndex === null) {
                this.rewardRules.push(item)
            } else {
                this.$set(this.rewardRules, this.editIndex, item)
            }
            this.$refs.editPopup.close()

            this.updateSet()
        },

        updateSet() {
            this.getOpenerEventChannel?.()?.emit?.('setComfirm', this.rewardRules)
        },

        diceValuesCheck(values) {
            if (values.length > 6) {
                this.$uni.showToast('骰子组合最多只能设置6位')
                return false
            }
            for (let i = 0, len = values.length; i < len; i++) {
                if (isNaN(values[i]) || values[i] < 1 || values[i] > 6) {
                    this.$uni.showToast('骰子组合每一个必须是1-6的整数')
                    return false
                }
            }
            return true
        },

        diceValuesRepeatCheck(values) {
            const rewardRules = JSON.parse(JSON.stringify(this.rewardRules))
            // 修改的时候，不能和自己做重复比较，不然肯定是重复
            if (this.editIndex !== null) rewardRules.splice(this.editIndex, 1)
            return rewardRules.some(item => item.dice_values.join('') === values.join(''))
        },

        async deleteRule(index) {
            const {confirm} = await this.$uni.showModal('确定移除该规则?', {showCancel: true})
            if (confirm) {
                this.rewardRules.splice(index, 1)
            }
        }
    }
}
</script>

<style lang="scss">
.page {
    background-color: #f8f8f8;
    min-height: 100vh;
    box-sizing: border-box;
}

.rules-count-add-role {
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 99;
    border-bottom: 10px solid #f8f8f8;
}

.rules {
    padding: 1px 0;
}

.rule-item {
    margin: 15px;
    padding: 10px;
    border-radius: 10px;
    background-color: #fff;

    .dice-value-images {
        padding: 10px 0;

        .dice-value-img {
            display: block;
            width: 44px;
            height: 44px;
        }
    }
}

.edit-popup {
    background-color: #fff;
    width: 300px;
    padding: 10px;
    border-radius: 10px;

    .form-description {
        position: relative;
        top: -4px;
    }

    .confirm-button {
        width: 150px;
        line-height: 40px;
        border-radius: 20px;
    }
}
</style>
