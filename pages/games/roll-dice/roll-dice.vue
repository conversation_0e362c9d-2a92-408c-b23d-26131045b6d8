<template>
    <view class="roll-dice-game" :style="{ backgroundImage: `url(${images.bgImg})` }">
        <!-- 游戏区域 -->
        <view class="game-area">
            <!-- 骰子碗容器 -->
            <view class="dice-bowl-container">
                <image :src="images.bowl" mode="widthFix" class="dice-bowl-image"/>

                <!-- 物理动画骰子 -->
                <view
                    v-for="(item, index) in diceList"
                    :key="index"
                    class="dice-physics-wrapper"
                    :style="{ left: `${item.left}%`, top: `${item.top}%` }"
                >
                    <view
                        class="dice-physics-item"
                        :style="{transform: item.physics.showResult ? 'none' : `translate3d(${item.physics.x}px, ${item.physics.y}px, 0) rotate(${item.physics.rotation}deg)`}"
                    >
                        <image
                            v-if="item.physics.showResult"
                            class="dice-result-image"
                            :src="images[`dice${item.value}`]"
                            mode="aspectFill"
                        />

                        <view v-else class="dice-animation-container">
                            <image
                                class="dice-animation-frame"
                                :style="{ opacity: idx == item.physics.randomDice ? 1 : 0 }"
                                v-for="(itm, idx) in diceImageList"
                                :key="idx"
                                :src="itm"
                                mode="aspectFill"
                            />
                        </view>
                    </view>
                </view>
            </view>

            <!-- 掷骰子按钮 -->
            <view
                class="roll-dice-button"
                :class="{ 'roll-dice-button--disabled': isRolling }"
                @click="rollDice"
            >
                <image :src="images.rollBtnImg" mode="widthFix" class="roll-dice-button__bg"/>
                <view class="roll-dice-button__text">掷骰子</view>
            </view>
        </view>

    </view>
</template>

<script>
import config from './config'

export default {
    name: "rill-dice",
    emits: ['rollOver'],
    props: {
        imagesConfig: {
            type: Object,
            default: () => ({})
        },

        // 是否允许摇骰子
        allowRoll: {
            type: Boolean,
            default: true
        }
    },

    data() {
        return {
            diceImageList: [],
            diceList: this.initDiceList(), // 骰子列表
            isRolling: false, // 是否正在投掷
        }
    },

    computed: {
        images() {
            const {
                bgImg: defaultBgImg,
                bowl: defaultBowlImg,
                rollBtnImg: defaultRollBtnImg,
                dice1: defaultDice1,
                dice2: defaultDice2,
                dice3: defaultDice3,
                dice4: defaultDice4,
                dice5: defaultDice5,
                dice6: defaultDice6
            } = config.images

            const {bgImg, bowl, rollBtnImg, dice1, dice2, dice3, dice4, dice5, dice6} = this.imagesConfig

            return {
                bgImg: bgImg || defaultBgImg,
                bowl: bowl || defaultBowlImg,
                rollBtnImg: rollBtnImg || defaultRollBtnImg,
                dice1: dice1 || defaultDice1,
                dice2: dice2 || defaultDice2,
                dice3: dice3 || defaultDice3,
                dice4: dice4 || defaultDice4,
                dice5: dice5 || defaultDice5,
                dice6: dice6 || defaultDice6
            }
        }
    },

    mounted() {
        const {dice1, dice2, dice3, dice4, dice5, dice6} = config.images
        this.diceImageList = [dice1, dice2, dice3, dice4, dice5, dice6]
    },

    methods: {
        // 初始化骰子列表
        initDiceList() {
            return config.diceList.map((dice, index) => ({
                ...dice,
                physics: {
                    ...this.createDicePhysics(),
                    randomDice: index,
                }
            }))
        },

        // 创建骰子物理属性
        createDicePhysics() {
            return {
                active: false,
                x: 0,
                y: 0,
                vx: 0,
                vy: 0,
                rotation: 0,
                rotationSpeed: 0,
                opacity: 1,
                settled: false,
                showResult: false,
                randomDice: 0,
            }
        },

        // 掷骰子
        rollDice() {
            if (this.isRolling || !this.allowRoll) return
            this.isRolling = true

            // 为每个骰子生成随机点数
            this.diceList.forEach((dice, index) => {
                // 延迟启动每个骰子的物理动画
                setTimeout(() => {
                    this.startDicePhysicsAnimation(dice)
                    dice.value = Math.ceil(Math.random() * 6)
                }, index * 100)
            })

            // 等待所有动画完成后结束游戏
            setTimeout(() => {
                this.isRolling = false
                this.endGame()
            }, config.physicsConfig.animationDuration + 600)
        },

        // 开始骰子物理动画
        startDicePhysicsAnimation(dice) {
            // 设置随机初始速度和旋转
            dice.physics = {
                active: true,
                x: (Math.random() - 0.5) * 30, // 随机初始X位置
                y: -40, // 从上方开始
                vx: (Math.random() - 0.5) * 6, // 随机水平速度
                vy: Math.random() * 2 + 1, // 随机垂直速度
                rotation: Math.random() * 360, // 随机初始旋转
                rotationSpeed: (Math.random() - 0.5) * 15, // 随机旋转速度
                opacity: 1,
                settled: false,
                showResult: false,
                randomDice: dice.physics.randomDice,
            }

            // 开始物理模拟
            this.runPhysicsSimulation(dice)
        },

        // 运行物理模拟
        runPhysicsSimulation(dice) {
            const startTime = Date.now()

            const animate = () => {
                const elapsed = Date.now() - startTime

                if (elapsed > config.physicsConfig.animationDuration || dice.physics.settled) {
                    // 动画结束，显示最终结果
                    dice.physics.showResult = true

                    setTimeout(() => {
                        dice.physics.active = false
                    }, 500)
                    return
                }

                // 更新物理状态
                this.updateDicePhysics(dice.physics)

                // 继续动画
                setTimeout(() => animate(), 16)
            }

            animate()
        },

        // 更新骰子物理状态
        updateDicePhysics(physics) {
            const {gravity, bowlRadius, diceSize, bounce, friction} = config.physicsConfig

            // 应用重力
            physics.vy += gravity

            // 更新位置
            physics.x += physics.vx
            physics.y += physics.vy

            // 更新旋转
            physics.rotation += physics.rotationSpeed

            // 碗边界碰撞检测（圆形边界）
            const distanceFromCenter = Math.sqrt(physics.x * physics.x + physics.y * physics.y)

            if (distanceFromCenter > bowlRadius - diceSize) {
                // 碰撞到碗壁
                const angle = Math.atan2(physics.y, physics.x)
                const normalX = Math.cos(angle)
                const normalY = Math.sin(angle)

                // 反射速度
                const dotProduct = physics.vx * normalX + physics.vy * normalY
                physics.vx -= 2 * dotProduct * normalX * bounce
                physics.vy -= 2 * dotProduct * normalY * bounce

                // 调整位置到边界内
                const targetDistance = bowlRadius - diceSize
                physics.x = normalX * targetDistance
                physics.y = normalY * targetDistance

                // 增加旋转速度
                physics.rotationSpeed += (Math.random() - 0.5) * 8
            }

            // 底部碰撞
            if (physics.y > 15) {
                physics.y = 15
                physics.vy *= -bounce
                physics.rotationSpeed *= 0.8
            }

            // 应用摩擦力
            physics.vx *= friction
            physics.vy *= friction
            physics.rotationSpeed *= friction

            // 检查是否静止
            const speed = Math.sqrt(physics.vx * physics.vx + physics.vy * physics.vy)
            if (speed < 0.1 && Math.abs(physics.rotationSpeed) < 1 && physics.y > 10) {
                physics.settled = true
                physics.vx = 0
                physics.vy = 0
                physics.rotationSpeed = 0
                physics.showResult = true
            }

            physics.randomDice = Math.floor(Math.random() * 6)
        },

        endGame() {
            const diceValues = this.diceList.map(({value}) => value)
            this.$emit('rollOver', diceValues)
        }
    }
}
</script>

<style lang="scss" scoped>
/* ===== 游戏主容器 ===== */
.roll-dice-game {
    width: 100%;
    min-height: 100vh;
    padding-top: 10vh;
    background-size: cover;
    background-repeat: no-repeat;
    overflow: hidden;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* ===== 游戏区域 ===== */
.game-area {
    position: relative;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* ===== 骰子碗容器 ===== */
.dice-bowl-container {
    width: 100%;
    border-radius: 0.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    
    .dice-bowl-image {
        width: 100%;
        height: auto;
    }
}

/* ===== 骰子物理动画 ===== */
.dice-physics-wrapper {
    position: absolute;
    width: 10vw;
    max-width: 64px;
    height: 10vw;
    max-height: 64px;
    pointer-events: none;
}

.dice-physics-item {
    position: absolute;
    width: 100%;
    height: 100%;
    transform-origin: center;
    transition: none;
}

.dice-result-image {
    width: 100%;
    height: 100%;
}

.dice-animation-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.dice-animation-frame {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}

/* ===== 掷骰子按钮 ===== */
.roll-dice-button {
    position: relative;
    width: 40%;
    margin-top: 2rem;
    display: flex;
    transition: all 0.3s ease;
    
    &:active {
        transform: scale(0.95);
        box-shadow: 0 2rpx 6rpx rgba(74, 144, 226, 0.2);
    }
    
    &--disabled {
        opacity: 0.6;
        pointer-events: none;
    }
    
    &__bg {
        width: 100%;
        height: auto;
    }
    
    &__text {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        color: #fff;
        letter-spacing: 2px;
        font-weight: 600;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
</style>