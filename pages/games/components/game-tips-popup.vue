<template>
    <view>
        <uni-popup ref="popup" :is-mask-click="false">
            <view class="game-tips bg-white">
                <view class="game-tips-title">{{ title }}</view>
                <view v-if="tipsList && tipsList.length" class="game-tips-content color-content">
                    <view v-for="(item, index) in tipsList" :key="index">{{ item }}</view>
                </view>

                <slot></slot>

                <view class="p10">
                    <view class="start-game-button" @click="startGame">{{ buttonText }}</view>
                </view>
            </view>

            <view v-if="showAd" class="flex-all-center pt5">
                <xwy-ad :ad_type="3"></xwy-ad>
                <xwy-ad :ad_type="66"></xwy-ad>
            </view>
        </uni-popup>

    </view>
</template>

<script>
export default {
    name: "game-tips-popup",
    emit: ['startGame'],
    props: {
        tipsList: {
            type: Array,
            default: () => []
        },
        showAd: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: '游戏说明'
        },
        buttonText: {
            type: String,
            default: '开始游戏'
        }
    },

    methods: {
        startGame() {
            this.$emit('startGame')
        },

        open() {
            this.$refs.popup.open()
        },

        close() {
            this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.game-tips {
    width: 90vw;
    border-radius: 10px;

    .game-tips-title {
        line-height: 44px;
        text-align: center;
    }

    .game-tips-content {
        padding: 0 20px;
        line-height: 30px;
    }

    .start-game-button {
        width: 150px;
        line-height: 40px;
        border-radius: 20px;
        text-align: center;
        font-size: 18px;
        background-color: #ff985e;
        color: #fff;
        margin: 10px auto;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
}
</style>