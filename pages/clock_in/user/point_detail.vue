<template>
    <view v-if="activity_detail" class="page">
        <xwy-ad
            v-if="activity_detail && (!activity_detail.rank_set || !activity_detail.rank_set.closed_AD)"
            :activity_id="id"
            :ad_type="3"
        />

        <view v-if="detail" class="detail">
            <view v-if="detail.conf && detail.conf.logo">
                <image class="logo" :src="detail.conf.logo" mode="widthFix"/>
            </view>
            <view class="p10">
                <view class="flex-kai">
                    <view>
                        <view class="color-title">{{ detail.name }}</view>

                        <!--活动id 6b49263ec07f39b4d7290d75b1818fc2 不显示打卡人次数 - 晓阳-->
                        <!--只答题不需要的不显示打卡人次数 - 晓阳-->
                        <view v-if="id !== '6b49263ec07f39b4d7290d75b1818fc2' && !just_exam">
                            <uni-tag
                                :text="clock_in_text + '人次 ' + clock_in_people_count + '人次'"
                                size="mini"
                                type="success"
                                :circle="true"
                                @click="lookPointSignRecord"
                            ></uni-tag>
                        </view>
                        <view
                            v-if="detail.conf && detail.conf.address"
                            class="color-content"
                            @click="openLocation"
                        >
                            <uni-icons type="location" size="16" color="#80848f"/>
                            <text class="color-sub font14">{{ detail.conf.address }}</text>
                        </view>
                    </view>
                    
                    <!--只答题的活动只能管理员从点位管理生成点位二维码进入，所以这里不能分享-->
                    <template v-if="!just_exam">
                        <!-- #ifndef H5 -->
                        <!-- 开启了只能通过二维码进入才能打卡的，除了管理员以外不能分享 -->
                        <view
                            v-if="is_admin || !activity_detail.rank_set || !activity_detail.rank_set['scan_need_books']"
                            class="flex-all-center"
                        >
                            <button class="share-btn" type="default" plain="true" size="mini"
                                    @click="showActiveSharePopup">
                                <text class="iconfont icon-share color-disabled font24"></text>
                            </button>
                        </view>
                        <!-- #endif -->
                    </template>
                </view>

            </view>

            <xwy-ad
                v-if="!sign_show && !success_info && activity_detail && (!activity_detail.rank_set || !activity_detail.rank_set.closed_AD)"
                :activity_id="id"
                :ad_type="4"
            />

            <view v-if="detail.exam_id && !just_exam && is_join" class="flex-kai p10">
                <view style="line-height: 40px;">
                    <text class="color-content" v-if="user_max_score !== null">
                        答题最高分: {{ user_max_score || 0 }}分
                    </text>
                    <text v-else class="color-sub">未参与答题</text>
                </view>
                <view class="to-exam-btn bg-light-primary font14 color-white"
                      hover-class="navigator-hover" @click="toExam">去答题
                </view>
            </view>

            <view v-if="lottery_id && have_sign_light" class="flex-kai p10">
                <view class="color-sub ellipsis" style="line-height: 40px; width: calc(100% - 120px);">
                    {{ lottery_title || '' }}
                </view>
                <view class="to-exam-btn bg-warning font14 color-white"
                      hover-class="navigator-hover" @click="toLottery">去抽奖
                </view>
            </view>

            <view v-if="news_content" class="content p10">
                <u-parse :content="news_content"/>
            </view>
        </view>


        <view style="height: 100px;"></view>

        <view class="bottom-button-view bg-white">
            <view class="flex-row">
                <view
                    v-if="show_back_btn && id"
                    class="icon-item"
                    hover-class="navigator-hover"
                    @click="backActivity"
                >
                    <text class="iconfont icon-go-back color-sub font24"></text>
                    <view class="color-sub font12">返回</view>
                </view>
                <view
                    v-if="detail.lat && detail.lng"
                    class="icon-item"
                    hover-class="navigator-hover"
                    @click="openLocation"
                >
                    <text class="iconfont icon-map color-sub font24"></text>
                    <view class="color-sub font12">去这里</view>
                </view>
                <view
                    v-if="!just_exam"
                    class="icon-item"
                    hover-class="navigator-hover"
                    @click="lookPointSignRecord"
                >
                    <text class="iconfont icon-sign-in-calendar color-sub font24"></text>
                    <view class="color-sub font12">{{ clock_in_text }}记录</view>
                </view>

                <view
                    hover-class="navigator-hover"
                    class="bottom-button text-center flex-all-center bg-primary color-white"
                    @click="showSign"
                >{{ button_text }}
                </view>
            </view>
        </view>

        <uni-transition
            :mode-class="['fade', 'slide-bottom']"
            :show="sign_show"
            :styles="{position: 'fixed', top: 0, left: 0, zIndex: 99}"
        >
            <view class="sign-page bg-white">
                <view class="p10" v-if="activity_detail.conf.active.memo_required !== 0">
                    <uni-easyinput
                        type="textarea"
                        v-model="memo"
                        :maxlength="800"
                        :placeholder="memo_placeholder"
                    ></uni-easyinput>
                </view>

                <view v-if="activity_detail.conf.active.step_required" class="flex-kai p10">
                    <view class="flex-row">
                        <view class="color-content" style="min-width: 73px;">运动步数:</view>
                        <view>
                            <text v-if="wechat_step === null" class="color-sub">未获取</text>
                            <text
                                v-else
                                :class="{'color-warning': wechat_step >= 10000, 'color-success': wechat_step < 10000}"
                            >{{ wechat_step || 0 }}
                            </text>
                        </view>
                    </view>
                    <view
                        class="color-primary text-right"
                        style="min-width: 70px;"
                        hover-class="navigaotr-hover"
                        @click="getStep"
                    >{{ wechat_step === null ? '获取步数' : '重新获取' }}
                    </view>
                </view>

                <view v-if="activity_detail.conf.active.pic_list_required !== 0" class="p10">
                    <view>
                        <text class="color-content">{{ clock_in_text }}相册</text>
                        <text v-if="activity_detail.conf.active.pic_list_required === 1" class="color-red">*
                        </text>
                        <text class="color-sub font12 pl5">
                            {{ pic_list.length }}/{{ pic_list_max_count }}
                        </text>
                    </view>
                    <view v-if="picSimilarOpen && picSimilarPercentLimit" 
                          class="color-sub font14" style="padding-top: 5px;" @click="lookPicSimilarSample">
                        <text>提示: 请按示例图片拍摄，图片相似度需超过{{ picSimilarPercentLimit }}%。</text>
                        <text class="color-primary pl5">查看示例图片</text>
                    </view>
                    <view style="padding-top: 5px;">
                        <view class="flex-row flex-wrap">
                            <view class="top-rank-banner-item" v-for="(item, index) in pic_list" :key="index">
                                <image :src="item" mode="aspectFill" @click="previewImage(pic_list, item)"/>
                                <view class="del-image-item" @click.stop="pic_list.splice(index, 1)">
                                    <uni-icons type="closeempty" color="#e20f04"/>
                                </view>
                            </view>
                            <view
                                v-if="pic_list.length < pic_list_max_count"
                                class="add-image text-center"
                                @click="addImage"
                            >
                                <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="activity_detail.conf.active.video_list_required !== 0" class="p10">
                    <view>
                        <text class="color-content">{{ clock_in_text }}视频</text>
                        <text v-if="activity_detail.conf.active.video_list_required === 1" class="color-red">
                            *
                        </text>
                    </view>
                    <view class="pt5">
                        <view v-if="video_list.length" class="video-item-view">
                            <video class="video-item" :src="video_list[0]"></video>
                            <view class="del-image-item" @click.stop="video_list = []">
                                <uni-icons type="closeempty" color="#e20f04"/>
                            </view>
                        </view>
                        <view v-else class="choose-video flex-all-center" @click="chooseVideo">
                            <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                        </view>
                    </view>
                </view>

                <view class="flex-all-center pt10">
                    <view class="clock-in flex-all-center bg-primary color-white"
                          hover-class="navigator-hover" @click="clockIn"
                    >{{ clock_in_text }}
                    </view>
                </view>
            </view>
        </uni-transition>


        <active-share ref="activeShare" qr-code-title="生成二维码"/>

        <view v-if="success_info" class="success flex-column flex-all-center text-center">
            <view class="main">
                <view class="top bg-primary color-white" style="padding: 50px 20px 20px;">
                    <icon type="success" size="80" color="#ffffff"></icon>
                    <view class="font18">{{ clock_in_text }}成功</view>
                </view>
                <view class="bottom2 bg-white" style="padding: 20px 20px 50px;">
                    <view class="pb10">{{ success_info }}</view>

                    <view v-if="detail.exam_id && mustExamOpen !== 2" class="flex-all-center pt15">
                        <view class="text-center bg-light-primary color-white"
                              style="width: 180px; line-height: 40px; border-radius: 20px;"
                              hover-class="navigator-hover" @click="toExam">去答题
                        </view>
                    </view>

                    <view v-if="lottery_id" class="flex-all-center pt15">
                        <view class="text-center bg-warning color-white"
                              style="width: 180px; line-height: 40px; border-radius: 20px;"
                              hover-class="navigator-hover" @click="toLottery">去抽奖
                        </view>
                    </view>

                    <view v-if="sign_lottery_id" class="flex-all-center pt15">
                        <view class="text-center bg-warning color-white"
                              style="width: 180px; line-height: 40px; border-radius: 20px;"
                              hover-class="navigator-hover" @click="toSignLottery">去抽奖
                        </view>
                    </view>
                </view>
            </view>

            <view class="pt5"></view>
            <xwy-ad
                v-if="activity_detail && (!activity_detail.rank_set || !activity_detail.rank_set.closed_AD)"
                :activity_id="id"
                :ad_type="66"
            />
            <view class="cancel text-center">
                <icon
                    type="cancel"
                    color="#bbbec4"
                    size="28"
                    @click="success_info = ''"
                />
            </view>

            <xwy-ad
                v-if="activity_detail && (!activity_detail.rank_set || !activity_detail.rank_set.closed_AD)"
                :activity_id="id"
                :ad_type="3"
            />
        </view>


    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import base64 from '@/utils/base64.js'

export default {
    data() {
        return {
            detail: null,
            distance_open: 1,
            distance: -1,
            news_content: '',
            sign_show: false,
            memo: '',
            pic_list: [],
            pic_list_max_count: 6,
            video_list: [],
            // video_list_max_count: 6,
            id: '',
            success_info: '',
            button_text: '打卡',
            activity_detail: null,
            wechat_step: null,
            is_admin: false,
            show_back_btn: false,   // 如果是转发进来的，整个页面栈只有当前页面，显示返回活动按钮
            clock_in_people_count: 0,
            clock_in_text: '打卡',
            have_sign_light: false,
            user_max_score: null,
            lottery_id: null,
            lottery_title: '',
            sign_lottery_id: null,
            just_exam: false,
            is_join: false
        }
    },
    
    computed: {
        picSimilarOpen() {
            return !!this.activity_detail?.rank_set?.['pic_similar']
        },
        picSimilarPercentLimit() {
            return Math.floor(this.activity_detail?.conf?.active?.similarity_percent || 0)
        },

        memo_placeholder() {
            const {memo_required, memo_placeholder} = this.activity_detail?.conf?.active || {}
            if (!memo_required) return ''
            if (memo_placeholder) return memo_placeholder
            return `请填写打卡备注,800字内,${memo_required === 2 ? '非' : ''}必填`
        },

        mustExamOpen() {
            return this.activity_detail?.conf?.active?.must_exam?.open || 0
        }
    },
    

    onLoad(e) {
        this.hideHomeButton()

        this.$uni.showLoading('加载中...')
        if (e.nfc) this.nfc_enter = true

        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            if (e.scene) return this.analysisScene(e.scene)

            this.id = e.id
            this.point_id = Number(e.point_id)


            this.init()
        })

        this.pagesCheck()
    },

    onShow() {
        if (this.detail?.exam_id && this.is_to_exam) {
            this.is_to_exam = false
            this.getExamScore(true)
        }
    },

    onShareAppMessage() {
        let url = '/pages/clock_in/user/point_detail?id=' + this.id + '&point_id=' + this.point_id
        let title = this.detail.name
        let imageUrl = this.detail.conf?.logo


        // 开启了只能从二维码进入才能打卡的活动，除了管理员以为，转发活动详情页面而不是点位详情页面
        if (!this.is_admin && this.activity_detail.rank_set?.['scan_need_books']) {
            url = '/pages/clock_in/user/detail?id=' + this.id
            title = this.activity_detail.name
            imageUrl = this.activity_detail.logo || ''
        }

        return {
            title,
            path: url,
            imageUrl
        }
    },

    methods: {
        hideHomeButton() {
            const pages = this.$uni.pages()
            if (pages.length === 1) uni.hideHomeButton()
        },

        async init() {
            await this.getActiveDetails()
            await this.getPointDetail()
            await this.getClockInPeopleCount()

            uni.hideLoading()

            this.nfcAutoSign()
        },
        
        async getActiveDetails() {
            let activity_detail = app.globalData['activity_detail'] || this.activity_detail

            if (!activity_detail) {
                const res = await xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.id
                    }
                })

                if (!res || !res['data'] || !res['data']['active_details']) {
                    xwy_api.alert(res && res['info'] || '详情获取失败')
                    return uni.hideLoading()
                }

                activity_detail = res['data']['active_details']
            }

            if (activity_detail.userid === app.globalData['userid']) this.is_admin = true
            if (activity_detail.types === 21) {
                this.clock_in_text = '集卡'
                this.button_text = '集卡'
            } else if (activity_detail?.rank_set?.['signOnlyExam']) {
                this.just_exam = true
                this.clock_in_text = '答题'
                this.button_text = '去答题'
                uni.hideShareMenu(undefined)  // 只能答题的活动只能管理员从点位管理生成点位二维码进入，所以这里不能分享
            }
            
            
            this.activity_detail = activity_detail  
        },
        
        pagesCheck() {
            const pages = getCurrentPages()
            if (pages.length === 1) this.show_back_btn = true
        },

        backActivity() {
            this.$uni.reLaunch(`./detail?id=${this.id}`)
        },

        analysisScene(scene) {
            this.from_qrcode = true
            const sceneStr = decodeURIComponent(scene)
            console.log(sceneStr)
            const id = utils.getUrlParams('id', sceneStr)
            const point_id = utils.getUrlParams('p_id', sceneStr)
            console.log('id===', id)
            console.log('point_id===', point_id)
            if (!id || !point_id) {
                uni.hideLoading()
                return this.$uni.showModal('从二维码获取id失败')
            }

            this.point_id = Number(point_id)

            this.getActiveId(id)
        },


        async getActiveId(id) {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/get_long_active_id_by_short_id',
                data: {id}
            })
            if (res?.['data']?.['long_active_id']) {
                this.id = res['data']['long_active_id']
                await this.init()
            } else {
                uni.hideLoading()
                await this.$uni.showModal(res?.info || '长id获取失败')
            }
        },

        async getPointDetail(reload = false) {
            let point_detail = this.detail
            if (!point_detail) {
                const res = await xwy_api.request({
                    url: 'front.flat.sport_step.active_list/map_point_details',
                    data: {
                        id: this.point_id
                    }
                })

                if (!res?.data?.['map_point_details']) {
                    uni.hideLoading()
                    await this.$uni.showModal(res?.info || '详情获取失败')
                    return false
                }

                point_detail = res.data['map_point_details']
            }
            this.detail = point_detail

            this.$uni.setNavigationBarTitle(point_detail.name)

            !reload && point_detail.news_id && await this.getNews()

            await this.getUserDetails()
            await this.getExamScore()

            this.getLottery()
        },

        async getUserDetails() {
            const user_details_res = await xwy_api.request({
                url: 'front.flat.sport_step.user/user_attend_details',
                data: {
                    active_id: this.id
                }
            })
            this.user_details = user_details_res?.data.user_details || {}
        },

        // 检查是否能打卡等操作
        async statusCheck() {
            if (!this.activeTimeCheck()) return false
            if (!this.userDetailsCheck()) return false
            if (!await this.pointCheck()) return false
            if (!this.nfcCheck()) return false
            if (!await this.scanNeedBooksCheck()) return false
            if (!this.signTimeCheck()) return false
            if (!await this.previousPointSignCheck()) return false
            if (!await this.previousPointExamCheck()) return false
            if (!this.jusetQrcodeSignCheck()) return false
            if (!await this.locationCheck()) return false
            if (!this.justNfcSignCheck()) return false
            if (!await this.continuousSignValidityTimeCheck()) return
            // 打卡点分类设置的限制查询
            if (!await this.categoryLimitCheck()) return

            if (!await this.weekSignLimitCheck()) return false

            uni.hideLoading()
            return true
        },

        activeTimeCheck() {
            const activity_detail = this.activity_detail

            let {begin_time, end_time} = activity_detail
            begin_time *= 1000
            end_time *= 1000
            const now_time = new Date().getTime()
            let tips = ''
            if (now_time < begin_time) tips = '未开始'
            if (now_time > end_time) tips = '已结束'
            if (tips) {
                this.showNotSignTips(`活动${tips}`)
                return false
            }

            return true
        },

        userDetailsCheck() {
            const user_details = this.user_details

            if (!user_details.id) {
                this.toJoinActive()
                return false
            }

            const activity_detail = this.activity_detail
            if (activity_detail.conf.active.enter_types === 3 && !user_details.checked) {
                this.showNotSignTips('需要管理员审核通过后才能' + this.clock_in_text)
                return false
            }

            this.is_join = true
            return true
        },

        async pointCheck() {
            this.$uni.showLoading()
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/check_user_have_sign',
                data: {
                    active_id: this.id,
                    id: this.point_id
                }
            })
            uni.hideLoading()

            const have_sign_light = res?.['data']?.['have_sign_light']
            if (have_sign_light) this.have_sign_light = true


            const sign_times_type = this.activity_detail?.conf?.active?.sign_times_type

            if (sign_times_type === 1 && have_sign_light) {
                this.showNotSignTips(`你已在此${this.clock_in_text}，无需再次${this.clock_in_text}`)
                return false
            }


            if (sign_times_type === 2) {
                const daily_submit_num = this.activity_detail?.conf?.active?.daily_submit_num || 1
                const todayPointClockInCount = await this.getTodayClockInPeopleCount()

                if (todayPointClockInCount) {
                    if (daily_submit_num === 1) {
                        this.showNotSignTips('今日已' + this.clock_in_text)
                        return false
                    } else {
                        if (todayPointClockInCount >= daily_submit_num) {
                           this.showNotSignTips(`今日已达到${this.clock_in_text}上限(${daily_submit_num}次)`)
                            return false
                        }
                    }
                }
            }

             const last_sign_time = res?.data?.user_sign_details?.create_time

            // 打卡活动开启了打卡点分类的，需要判断24小时内不能重复打卡。定制开发的功能
            if (this.detail.category_id && last_sign_time && this.activity_detail.types === 5) {
                // 两次打卡时间不能小于24小时
                const lastTime = new Date(last_sign_time.replace(/-/g, '/')).getTime()
                const nowTime = new Date().getTime()
                const hours24 = 24 * 60 * 60 * 1000
                if (nowTime - lastTime < hours24) {
                    this.showNotSignTips('24小时内只能打卡一次')
                    return false
                }
            }

            return true
        },

        nfcCheck() {
            const auto_stamp = this.activity_detail?.conf?.active?.auto_stamp
            if (auto_stamp) {
                this.showNotSignTips(`需要通过NFC进入才能${this.clock_in_text}`)
                return false
            }

            return true
        },

        async scanNeedBooksCheck() {
            const scan_need_books = this.activity_detail?.rank_set?.['scan_need_books']

            if ((scan_need_books || this.just_exam) && !this.from_qrcode) {
                this.showNotSignTips('需要通过扫二维码进入才能' + this.clock_in_text)
                return false
            }

            if (scan_need_books) {
                this.$uni.showLoading()
                const books_date = await this.getBooksDate()
                uni.hideLoading()

                if (!books_date.length) {
                    this.showNotSignTips('没有报名今天的活动，无法' + this.clock_in_text)
                    return false
                }
            }

            return true
        },

        signTimeCheck() {
            let {exchange_start_time, exchange_end_time} = this.activity_detail?.conf?.active || {}
            if (exchange_start_time || exchange_end_time) {
                exchange_start_time ||= '00:00:00'
                exchange_end_time ||= '23:59:59'

                const now = Date.now()
                const today = this._utils.getDay(0, true, '/')
                const start_time = new Date(`${today} ${exchange_start_time}`).getTime()
                const end_time = new Date(`${today} ${exchange_end_time}`).getTime()

                let status = ''
                if (now < start_time) status = 'no_start'
                if (now > end_time) status = 'is_end'

                if (status !== '') {
                    this.showNotSignTips(status === 'no_start' ? `${exchange_start_time}后才能打卡` : `${exchange_end_time}后不允许打卡`)
                    return false
                }
            }

            return true
        },

        async previousPointSignCheck() {
            const sequential_clock_in = this.activity_detail?.conf?.active?.sequential_clock_in
            if (sequential_clock_in) {
                const previousPointNotCheckedIn = await this.previousPointNotCheckedIn()
                if (!previousPointNotCheckedIn) {
                    this.showNotSignTips(`上一${this.clock_in_text}点没有${this.clock_in_text}，请前往上一${this.clock_in_text}点${this.clock_in_text}`)
                    return false
                }
            }

            this.$uni.showLoading()
            const teamSignAll = await this.teamIsSignAll()
            uni.hideLoading()

            if (!teamSignAll) this.showNotSignTips('所在队伍上一打卡点还有队员没有打卡')

            return teamSignAll
        },

        // 检查队伍上一个打卡点是否全部打卡完成
        async teamIsSignAll() {
            if (!this.activity_detail?.rank_set?.team_group_open) return true
            if (!this.activity_detail?.conf?.active?.must_team_person_finish_clock_in) return true

            let teamSignData = await this.getPageTeamSignData()
            teamSignData ||= await this.getTeamSignDataFromApi()

            const {point_list, team_person} = teamSignData
            const point_index = point_list.findIndex(v => v.id === this.point_id)
            if (point_index === 0) return true
            const previous_point = point_list[point_index - 1]
            if (!previous_point) return true

            return previous_point.this_point_sign_person_num >= team_person
        },

        async getPageTeamSignData() {
            const just_one_page = this.$uni.pages().length === 1
            if (just_one_page) return null

            return new Promise(resolve => {
                this.getOpenerEventChannel().once('teamSignData', data => {
                    resolve(data)
                })
            })
        },

        async getTeamSignDataFromApi() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/user_sign_light_map_point',
                data: {
                    active_id: this.id
                }
            })

            const team_person = res?.data?.team_details?.['attend_person']
            if (!team_person && team_person !== 0) return null
            const point_list = res?.data?.['sign_light_res']
            if (!point_list?.length) return null

            return {
                team_person,
                point_list: point_list.map(v => ({
                    id: v.id,
                    this_point_sign_person_num: v.this_point_sign_person_num,
                }))
            }
        },

        async previousPointExamCheck() {
            if (this.just_exam) return true
            return await this.examChecked()
        },

        async examChecked() {
            const open = this.mustExamOpen
            if (!open) return true

            this.$uni.showLoading()
            const point_list = await this.getPointSignList()
            uni.hideLoading()

            if (!point_list.length) return true

            const current_point_index = point_list.findIndex(v => v.id === this.point_id)

            if (open === 1) {
                const sequential_clock_in = this.activity_detail?.conf?.active?.sequential_clock_in
                if (!sequential_clock_in) return true
                if (current_point_index === 0) return true
                const previous_point = point_list[current_point_index - 1]
                if (!previous_point?.exam_id) return true
                if (!previous_point?.['user_submit_exam']) {
                    this.showNotSignTips(`上一${this.clock_in_text}点没有答题，请前往上一${this.clock_in_text}点答题`)
                    return false
                }
                const score = Number(this.activity_detail?.conf?.active?.must_exam?.score || 0)
                const user_score = previous_point?.['user_submit_exam']?.score || 0
                if (user_score < score) {
                   this.showNotSignTips(`上一${this.clock_in_text}点答题未通过，请前往上一${this.clock_in_text}点答题`)
                }
                return user_score >= score
            }


            if (open === 2) {
                const current_point = point_list[current_point_index]
                if (!current_point?.exam_id) return true
                if (!current_point?.['user_submit_exam']) {
                    this.showNotSignTips('请答题后再打卡')
                    return false
                }
                const score = Number(this.activity_detail?.conf?.active?.must_exam?.score || 0)
                const user_score = current_point?.['user_submit_exam']?.score || 0
                if (user_score < score) this.showNotSignTips(`答题分数需要达到${score}才能打卡`)

                return user_score >= score
            }

            return true
        },

        async locationCheck() {
            const {open, distance: set_distance} = this.activity_detail?.conf?.active?.clock_in_on_site || {}
            if (!open) return true

            this.$uni.showLoading()
            const location = await this.getLocation()
            uni.hideLoading()

            if (!location) {
                this.showNotSignTips('定位失败,无法' + this.clock_in_text)
                return false
            }

            const {latitude, longitude} = location
            this.user_location = {latitude, longitude}

            const {lat, lng} = this.detail
            const distance = utils.getDistance(latitude, longitude, Number(lat), Number(lng)) / 1000
            this.distance = distance


            if (distance > set_distance) {
                this.showNotSignTips(`距离${this.clock_in_text}点${distance}公里,不在${this.clock_in_text}范围,无法${this.clock_in_text}`)
            }

            return distance <= set_distance
        },

        getLocation() {
            return new Promise(resolve => {
                uni.getLocation({
                    type: 'gcj02',
                    altitude: true,
                    success: res => resolve(res),
                    fail: err => {
                        this.$uni.showModal('定位失败' + JSON.stringify(err))
                        resolve(false)
                    }
                })
            })
        },

        jusetQrcodeSignCheck() {
            if (!this.activity_detail?.conf?.active?.just_qrcode_sign) return true
            if (this.from_qrcode) return true
            this.showNotSignTips(`需要通过扫${this.clock_in_text}点二维码进入才能${this.clock_in_text}`)
            return false
        },
        
        showNotSignTips(tips) {
            uni.hideLoading()

            if (tips.length > 20) {
                this.$uni.showModal(tips)
                return
            }

            let duration = 1500
            if (tips.length > 5) duration = tips.length * 500

            this.$uni.showToast(tips, 'none', duration)
        },


        getLottery() {
            if (!this.user_details.checked) return   // 没有参与活动或者报名未审核
            if (!this.activity_detail.rank_set?.['lottery_open']) return   // 活动没有开启抽奖功能
            if (!this.detail.conf?.lottery?.lottery_id) return   // 打卡点没有设置抽奖
            this.lottery_id = this.detail.conf.lottery.lottery_id
            if (this.detail['lottery_details']?.title) this.lottery_title = this.detail['lottery_details'].title
        },

        toLottery() {
            this.$uni.navigateTo('/pages/lottery/user/lottery?id=' + this.lottery_id + '&active_id=' + this.id + '&point_id=' + this.point_id)
            this.success_info = ''
        },

        async getExamScore(reload) {
            if (!this.detail.exam_id || this.just_exam) return
            this.$uni.showLoading()
            const point_list = await this.getPointSignList(reload)
            if (!point_list.length) return uni.hideLoading()
            const current_point = point_list.find(v => v.id === this.point_id)
            if (!current_point?.['user_submit_exam']) return uni.hideLoading()
            this.user_max_score = current_point['user_submit_exam'].score || 0

            uni.hideLoading()
        },

        // 开启了按点位顺序打卡，检查上一个点位是否打卡
        async previousPointNotCheckedIn() {
            const point_list = await this.getPointSignList()
            if (!point_list.length) return true
            const current_point_index = point_list.findIndex(v => v.id === this.point_id)
            if (current_point_index === 0) return true
            const previous_point = point_list[current_point_index - 1]
            return previous_point['have_sign_light']
        },

        // 获取各个点位的打卡状态
        async getPointSignList(reload = false) {
            if (!reload && this.point_sign_list) return this.point_sign_list
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/user_sign_light_map_point',
                data: {
                    active_id: this.id
                }
            })
            const point_sign_list = res?.['data']?.['sign_light_res'] || []
            this.point_sign_list = point_sign_list
            return point_sign_list
        },


        async getClockInPeopleCount() {
            if (this.just_exam) return
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/public_square_sign_list',
                data: {
                    active_id: this.id,
                    checked: 1,
                    point_id: this.point_id
                }
            })

            this.clock_in_people_count = res?.['data']?.['user_sign_list']?.['total'] || 0
        },


        async getTodayClockInPeopleCount() {
            const today = this._utils.getDay(0, true, '-')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/public_square_sign_list',
                data: {
                    active_id: this.id,
                    point_id: this.point_id,
                    is_myself: 1,
                    begin_time: `${today} 00:00:00`,
                    end_time: `${today} 23:59:59`,
                    page: 1,
                    perpage: 1
                }
            })

            return res?.data?.user_sign_list?.total || 0
        },

        async getBooksDate() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.scanPointBooks/user_submit_books_date_list',
                data: {
                    active_id: this.id,
                    date: utils.getDay(0, true)
                }
            })


            return res?.['data']?.['submit_books_date_list']?.data || []
        },

        async getNews() {
            const res = await xwy_api.request({
                url: 'front.news/news_details',
                data: {
                    news_id: this.detail.news_id
                }
            })

            let content = res?.['data']?.['news_details']?.content
            if (!content) return false

            content = utils.newsContentInit(content)

            this.news_content = content
        },

        toExam() {
            const exam_open = this.mustExamOpen
            if (exam_open === 1 && !this.have_sign_light && !this.just_exam) {
                return this.showNotSignTips('请完成打卡以后再来答题')
            }

            this.$uni.navigateTo(`/pages/likou_dati/pages/exam/exam_details/exam_details?exam_id=${this.detail.exam_id}&active_id=${this.id}&point_id=${this.point_id}`)
            this.is_to_exam = true
            this.success_info = ''
        },

        openLocation() {
            const latitude = Number(this.detail.lat)
            const longitude = Number(this.detail.lng)

            if (!latitude || !longitude || isNaN(latitude) || isNaN(longitude)) return false

            uni.openLocation({
                latitude,
                longitude,
                name: this.detail.name,
                address: this.detail.conf.address
            })
        },

        lookPointSignRecord() {
            uni.navigateTo({
                url: `./public_sign_list?id=${this.id}&point_id=${this.point_id}&point_name=${this.detail.name}&type=${this.activity_detail.types}`
            })
        },

        getStep() {
            this.$uni.showLoading('步数获取中...')
            xwy_api.getWeRunData(res => {
                uni.hideLoading()
                if (res?.data?.['crypt_data']?.['stepInfoList']?.length) {
                    const step_list = res.data['crypt_data']['stepInfoList']
                    const today_step = step_list[step_list.length - 1].step

                    if (today_step === this.wechat_step) {
                        this.$uni.showToast('步数没有变化')
                    }

                    this.wechat_step = today_step || 0
                } else {
                    this.$uni.showModal('运动步数获取失败')
                }
            })
        },

        addImage() {
            this.$uni.navigateTo('/pages/other/image_upload_or_select?active_id=' + this.id, {
                events: {
                    newImg: src => this.$nextTick(() => this.imageSimilarityCheck(src))
                }
            })
        },


        async imageSimilarityCheck(url) {
            const {picSimilarOpen, picSimilarPercentLimit} = this
            if (!picSimilarOpen) return this.pic_list.push(url)
            if (isNaN(picSimilarPercentLimit) || !picSimilarPercentLimit) return this.pic_list.push(url)

            this.$uni.showLoading('图片比对中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.active.system_dev.ai.pic.similar_search.handelPic/similar_pic_result',
                data: {
                    url,
                    active_id: this.id,
                    point_id: this.point_id
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '图片比对失败')

            const percent = res?.data?.['similar_res']?.['percent_num']
            if (!percent || percent < picSimilarPercentLimit) {
                return this.$uni.showModal(`图片相似度低于${picSimilarPercentLimit}%,请重新上传。(图片相似度: ${Number(percent.toFixed(2))}%)`)
            }

            this.pic_list.push(url)
        },


        lookPicSimilarSample() {
            this.$uni.navigateTo(`/pages/clock_in/admin/point/point-picture/list?active_id=${this.id}&point_id=${this.point_id}`)  
        },


        async chooseVideo() {
            const video_data = await new Promise(resolve => {
                uni.chooseVideo({
                    success: res => {
                        return resolve(res)
                    }
                })
            })

            this.$uni.showLoading('视频上传中...')

            const video_src = await xwy_api.uploadVideo(video_data, this.activity_detail.active_id)

            uni.hideLoading()

            if (!video_src) {
                await this.$uni.showModal('视频上传失败，请重试')
                return false
            }

            this.video_list.push(video_src)
        },

        previewImage(urls, current = urls[0]) {
            uni.previewImage({
                urls,
                current
            })
        },


        showActiveSharePopup() {
            this.$refs.activeShare.open({
                page: 'pages/clock_in/user/point_detail',
                scene: 'id=' + this.activity_detail.id + '&p_id=' + this.point_id,
                qrcode_logo: this.detail.rank_set?.closed_AD && this.detail.conf?.active?.qrcode_logo
            })
        },

        async toJoinActive() {
            // 集卡活动不需要返回报名
            // if (this.activity_detail.types === 21) return

            const detailsPageRoute = 'pages/clock_in/user/detail'
            const detailsPage = this.$uni.pages().find(item => item.route === detailsPageRoute)

            let res = null
            if (this.id === '851da9142d037c7c8fc8dab066ba2735') {
                // 晓阳客户的集卡活动，未报名跳转提示
                res = await this.$uni.showModal('未参加活动，是否前往活动首页报名？', {
                    showCancel: true,
                    confirmText: '去报名'
                })
            } else {
                res = await this.$uni.showModal(`未参加活动，是否${detailsPage ? '参加' : '进入'}活动？`, {
                    showCancel: true,
                    confirmText: `${detailsPage ? '参加' : '进入'}活动`
                })
            }

            if (!res?.confirm) return

            if (detailsPage) {
                detailsPage.$vm.joinActivity()
                this.$uni.navigateBackPage(detailsPageRoute)
            } else {
                this.$uni.reLaunch(`./detail?id=${this.id}`)
            }
        },

        justNfcSignCheck() {
            if (!this.activity_detail.rank_set?.openNFC) return true
            if (!this.activity_detail.conf?.active?.just_nfc_sign) return true

            if (!this.nfc_enter) {
                this.$uni.showModal('请从NFC标签进入打卡')
                return false
            }

            return true
        },

        nfcAutoSign() {
            if (!this.nfc_enter) return
            if (!this.activity_detail.rank_set?.openNFC) return
            if (!this.activity_detail.conf?.active?.auto_sign_nfc) return

            this.showSign()
        },

        async showSign() {
            if (!await this.statusCheck()) return

            if (this.just_exam) return this.toExam()

            const {memo_required, pic_list_required, video_list_required} = this.activity_detail.conf.active
            if (memo_required === 0 && pic_list_required === 0 && video_list_required === 0) {
                await this.clockIn()
                return
            }
            this.sign_show = true
        },

        async continuousSignValidityTimeCheck() {
            if (!this.activity_detail.rank_set?.submitSignBetweenThirtyMinutes) return true

            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/get_user_sign_details',
                data: {
                    active_id: this.id,
                    point_id: this.point_id
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) {
                this.$uni.showModal(res?.info || '不能在此打卡')
                return false
            }
            const can_sign_this_point = res?.data?.sign_details?.can_sign_this_point
            if (!can_sign_this_point) this.$uni.showModal('不能在此打卡')
            return can_sign_this_point
        },

        // 打卡点分类设置的限制查询
        async categoryLimitCheck() {
            // OA没有开启打卡点分类，不用查询
            if (!this.activity_detail?.rank_set?.['mapPointCategorySet']) return true

            // 打卡点没有绑定分类，不用查询
            if (!this.detail.category_id) return true

            this.$uni.showLoading()
            // 分类设置的每月打卡限制次数查询
            const category = await this.getCategoryDetails(this.detail.category_id)
            const limit_num = category?.conf_set?.submit_sign?.limit_num
            // 没有限制次数，直接打卡
            if (!limit_num) {
                uni.hideLoading()
                return true
            }

            // 用户在当前点位的打卡记录
            const records = await this.getUserPointSignRecords(this.detail.category_id)
            uni.hideLoading()
            // 没有打卡记录，直接打卡
            if (!records.length) return true

            // 检查当月打卡次数
            const {year, month} = this._utils.getYearMonthDay()
            const signCount = records.filter(item => item.year === year && item.month === month).length
            if (signCount >= limit_num) {
                this.showNotSignTips(`${this.detail.name}每月只能打卡${limit_num}次`)
                return false
            }

            return true
        },

        async getCategoryDetails(id) {
            const res = await this.xwy_api.request({
                url: 'front.user.category/category_details',
                data: {
                    category_id: id
                }
            })

            return res?.data?.category_details || null
        },

        async getUserPointSignRecords() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/public_square_sign_list',
                data: {
                    active_id: this.id,
                    is_myself: 1,
                    point_id: this.point_id,
                    page: 1,
                    perpage: 1000
                }
            })

            const list = res?.data?.['user_sign_list']?.data || []
            return list.map(item => {
                const {year, month} = this._utils.getYearMonthDay(item.create_time)
                return {
                    create_time: item.create_time,
                    year,
                    month
                }
            })
        },

        async weekSignLimitCheck() {
            const limit = Math.floor(this.activity_detail.conf.active.week_sign_limit || 0)
            if (isNaN(limit) || !limit) return true

            const count = await this.getUserWeekSignCounts()

            if (count >= limit) {
                this.showNotSignTips(`每周只能打卡${limit}次，你本周已打卡${count}次，无法打卡。`)
                return false
            }

            return true
        },

        async getUserWeekSignCounts() {
            const weekDate = this._utils.getWeekDate(this._utils.getDay(0, true, '/'), true, '-')
            const begin_time = weekDate[0] + ' 00:00:00'
            const end_time = weekDate[weekDate.length - 1] + ' 23:59:59'

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/public_square_sign_list',
                data: {
                    active_id: this.id,
                    is_myself: 1,
                    page: 1,
                    perpage: 1,
                    begin_time,
                    end_time
                }
            })

            return res?.data?.user_sign_list?.total || 0
        },

        dataCheck() {
            const {
                memo_required,
                memo_min_words,
                pic_list_required,
                video_list_required,
                step_required
            } = this.activity_detail.conf.active
            if (memo_required === 1) {
                if (!this.memo) {
                    this.$uni.showToast('请填写备注')
                    return false
                }

                const min = Number(memo_min_words)
                if (min && this.memo.length < min) {
                    this.$uni.showToast(`备注不得少于${min}字`)
                    return false
                }
            }
            if (pic_list_required === 1 && !this.pic_list.length) {
                this.$uni.showToast('请上传相册')
                return false
            }
            if (video_list_required === 1 && !this.video_list.length) {
                this.$uni.showToast('请上传相册')
                return false
            }

            if (step_required) {
                const min_step = Number(this.activity_detail.conf.active.min_step)
                if (min_step) {
                    if (this.wechat_step === null) {
                        this.$uni.showToast('请获取运动步数')
                        return false
                    }
                    if (this.wechat_step < min_step) {
                        this.$uni.showModal('运动步数不得小于' + min_step + '步', {Title: this.clock_in_text + '失败'})
                        return false
                    }
                }
            }

            const data = {
                point_id: this.detail.id
            }
            if (this.user_location) {
                data.lat = this.user_location.latitude
                data.lng = this.user_location.longitude
            }
            if (this.memo) data.memo = this.memo

            const conf_json = {}
            if (this.pic_list.length) conf_json.pic_list = this.pic_list
            if (this.video_list.length) conf_json.video_list = this.video_list
            if (this.wechat_step) conf_json.wechat_step = this.wechat_step

            let conf_str = JSON.stringify(conf_json)
            conf_str = conf_str.replace(/·/g, '-')
            data.conf_json = base64['encode'](conf_str)


            return data
        },

        async clockIn() {
            const data = this.dataCheck()
            if (!data) return false

            this.$uni.showLoading(this.clock_in_text + '中...')

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/submit_user_point_sign',
                data
            })


            if (res?.status !== 1) {
                uni.hideLoading()
                return this.$uni.showModal(res?.info || `${this.clock_in_text}失败`)
            }

            this.memo = ''
            this.pic_list = []
            this.video_list = []


            this.sign_show = false

            this.sign_lottery_id = null

            this.signLotteryCheck()

            await this.stepExchange()

            this.signSuccess()

            this.success_info = res?.info || `${this.clock_in_text}成功`
            uni.hideLoading()
        },

        // 打卡成功，更新页面及列表数据
        signSuccess() {
            this.getPointDetail(true)
            this.getClockInPeopleCount()
            const eventChannel = this.getOpenerEventChannel()
            eventChannel && eventChannel.emit && eventChannel.emit('reloadList')
        },

        async stepExchange() {
            const {gift_goods, other_add_sport_step} = this.activity_detail?.rank_set || {}
            if (!gift_goods || !other_add_sport_step) return

            if (!this.activity_detail?.conf?.active?.integral?.exchange_step) return

            await this.xwy_api.getWeRunData()
            await this.wait(1000)
            await this.xwy_api.request({
                url: 'front.flat.sport_step.exchange/exchange_step',
                data: {
                    active_id: this.id
                }
            })
        },

        wait(time) {
            return new Promise(resolve => {
                setTimeout(() => {
                    resolve()
                }, time)
            })
        },

        signLotteryCheck() {
            const {rank_set = {}, conf = {}} = this.activity_detail || {}
            if (!rank_set?.['sign_num_lottery']) return
            const rules = conf.active?.sign_set?.rules ||[]
            if (!rules.length) return
            
            let sign_count = this.user_details?.sign_count || 0
            sign_count++
            
            const lottery = rules.find(item => item.num === sign_count)
            if (lottery?.lottery_active_id) this.sign_lottery_id = lottery.lottery_active_id
        },

        toSignLottery() {
            this.$uni.navigateTo(`/pages/lottery/user/lottery?type=sign&id=${this.sign_lottery_id}&active_id=${this.id}`)
        }
    }
}
</script>

<style>
.logo {
    width: 100vw;
    height: auto;
    display: block;
}

.share-btn {
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    width: 40px;
    min-width: 40px;
    height: 40px;
    line-height: 40px !important;
}

.bottom-button-view {
    position: fixed;
    z-index: 9;
    bottom: 0;
    left: 0;
    width: 100vw;
    box-sizing: border-box;
    border-top: 1px solid #eee;
    padding: 10px 10px 15px;
}

.icon-item {
    width: 55px;
    min-width: 55px;
    text-align: center;
}

.bottom-button {
    width: 100%;
    height: 40px;
    border-radius: 20px;
    margin-left: 10px;
}


.sign-page {
    width: 100vw;
    height: 100vh;
    box-sizing: border-box;
}


.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
    line-height: calc((100vw - 50px) / 3);
    margin: 5px;
}

.del-image-item {
    position: absolute;
    right: 10px;
    top: 8px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}


.top-rank-banner-item {
    padding: 5px;
    position: relative;
}

.top-rank-banner-item image {
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
}

.top-rank-banner-item .del-image-item {
    right: 8px;
}

.video-item-view {
    position: relative;
}

.video-item {
    width: 100%;
}

.choose-video {
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
    margin: 5px;
    border: 1px solid #eee;
}

.clock-in {
    width: 200px;
    height: 40px;
    border-radius: 20px;
}

.success {
    position: fixed;
    z-index: 99;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, .7);
}

.success .main {
    width: 300px;
    border-radius: 10px;
    overflow: hidden;
}

.to-exam-btn {
    width: 100px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
    margin-left: 10px;
    text-align: center;
}
</style>
