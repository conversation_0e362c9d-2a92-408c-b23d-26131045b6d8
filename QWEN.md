# 健步走 - uni-app微信小程序 (Qwen Code Context)

## 项目概述

这是一个功能丰富的综合性运动健康管理微信小程序，名为“健步走”，基于 `uni-app` 框架 (Vue 2.6.14) 开发，支持多平台发布。小程序以健步走活动为核心，集成了游戏中心、用户系统、朗诵活动、打卡功能等多种模块。

### 核心功能

- **多活动类型**: 健步走、AI运动、朗诵活动、打卡、投票等。
- **游戏中心**: 内置20+种小游戏（如2048、五子棋、贪吃蛇、飞机大战、成语接龙等）。
- **社交互动**: 运动圈、排行榜、分享。
- **企业定制**: 活动管理、数据导出、团队管理。
- **数据统计**: 运动数据分析。

### 技术栈

- **前端框架**: `uni-app` (Vue 2)
- **UI**: uni-app官方组件 + 自定义组件, SCSS
- **状态管理**: Vue.js 响应式
- **图表**: `qiun-data-charts`
- **后端服务**: PHP API (`https://sport.xwykj.com`) + 微信云开发 (wx.cloud)
- **AI服务**: AI运动识别插件
- **构建工具**: HBuilderX, Webpack

## 项目结构

```
step-uniapp-wxcloud/
├── 📁 pages/                  # 页面文件（分包架构）
│   ├── 📁 index/              # 首页
│   ├── 📁 activity/           # 健步走活动模块
│   │   ├── 📁 user/           # 用户端页面
│   │   └── 📁 admin/          # 管理端页面
│   ├── 📁 games/              # 游戏中心 (20+小游戏)
│   ├── 📁 user/               # 用户中心
│   ├── 📁 voice/              # 朗诵活动
│   ├── 📁 clock_in/           # 打卡功能
│   ├── 📁 vote/               # 投票系统
│   ├── 📁 shop/               # 商城系统
│   ├── 📁 ai_sport/           # AI运动
│   └── 📁 ...                 # 其他功能模块 (news, comment, lottery, etc.)
├── 📁 components/             # 公共组件
├── 📁 utils/                  # 工具函数 (API封装, 通用工具)
│   ├── 📁 api/                
│   │   ├── xwy_api.js        # 主要API接口封装和业务逻辑
│   │   └── uniapp_api.js     # uni-app API封装
├── 📁 config/                 # 配置文件
│   ├── config.js             # 主配置文件 (活动路径, 功能开关)
│   └── activity-page-path.js # 活动页面路径配置
├── 📁 static/                 # 静态资源 (样式, 图片)
├── 📁 uni_modules/            # uni-app插件模块
├── 📄 核心配置文件
│   ├── App.vue               # 应用入口文件 (全局数据, 初始化)
│   ├── main.js               # 项目入口文件 (Vue实例, 全局组件/方法注册)
│   ├── manifest.json         # 应用配置 (AppID, 小程序设置)
│   └── pages.json            # 页面路由配置 (分包, 页面样式)
```

## 开发与运行

### 环境要求

- Node.js >= 14.0.0
- npm >= 6.0.0
- HBuilderX >= 3.0.0
- 微信开发者工具 >= 1.05.2107300

### 启动流程

1.  **入口文件**: `App.vue` 是小程序的入口。它负责初始化全局数据（如 `who` 项目标识, `apihost` API域名, 用户信息等），处理微信登录、云开发初始化、以及一些全局事件监听。
2.  **页面路由**: `pages.json` 定义了所有页面路径和窗口样式，并通过 `subPackages` 实现了分包加载。
3.  **首页逻辑**: `pages/index/index.vue` 是小程序的启动页面。它在 `onLoad` 时调用 `this.$login.uniLogin` 进行用户登录授权，登录成功后根据配置决定跳转到哪个主页面（通常是 `/pages/diy-page/diy-index` 或特定活动页）。
4.  **工具与API**: `main.js` 中引入了 `xwy_api.js` (主要API交互和业务逻辑)、`utils.js` (通用工具函数)、`uni-api.js` (uni-app API封装) 等工具，并将其挂载到 Vue 原型上，方便全局使用 (`this.xwy_api`, `this._utils`, `this.$uni`)。
5.  **配置**: `config.js` 包含了项目级别的配置，如活动路径映射、功能开关、默认图片地址等。

### 开发指南

- **分包架构**: 项目采用分包结构，需注意分包大小限制（单个分包不超过2M）。
- **API调用**: 统一通过 `this.xwy_api.request` 或 `this.xwy_api.ajax` 进行后端API请求。
- **组件化**: 公共组件放在 `components/` 目录下。
- **样式**: 使用 SCSS 预处理器，公共样式在 `static/style/public.css`。

### 构建与部署

- 使用 HBuilderX 进行开发和构建。
- 通过 HBuilderX 的 "发行" -> "小程序-微信" 功能打包。
- 在微信开发者工具中上传代码、提交审核、发布。

## 重要文件说明

- **App.vue**: 应用全局配置和初始化逻辑。
- **main.js**: 项目入口，注册全局工具和组件。
- **pages.json**: 页面路由和分包配置。
- **manifest.json**: 小程序基础配置（AppID, 云开发等）。
- **utils/api/xwy_api.js**: 核心API交互、网络请求、用户登录、图片上传等逻辑。
- **config/config.js**: 项目级配置，包括活动路径、功能开关等。
- **pages/index/index.vue**: 应用启动和初始页面跳转逻辑。
